package bench

import (
	"fmt"
	"math"
	"sync"
	"time"
)

// 计算标准差 (适用于 int64 输入)
func StandardDeviationInt64(data []int64) float64 {
	n := float64(len(data))
	if n == 0 {
		return 0
	}

	// 计算均值
	var sum int64
	for _, v := range data {
		sum += v
	}
	mean := float64(sum) / n

	// 计算方差
	var varianceSum float64
	for _, v := range data {
		varianceSum += math.Pow(float64(v)-mean, 2)
	}
	variance := varianceSum / n

	// 返回标准差
	return math.Sqrt(variance)
}

func MeanInt64(data []int64) float64 {
	if len(data) == 0 {
		return 0
	}

	var sum int64
	for _, v := range data {
		sum += v
	}

	return float64(sum) / float64(len(data))
}

func funcCost(f func()) time.Duration {
	st := time.Now()
	f()

	return time.Now().Sub(st)
}

func Benchmark(f func(), concurrency int, num int) string {

	g := sync.WaitGroup{}

	records := make([]int64, 0, (int64(num) * int64(concurrency)))
	max := int64(0)
	min := int64(0)
	lock := &sync.Mutex{}

	for k := 0; k < concurrency; k++ {
		g.Add(1)
		go func() {
			defer g.Done()
			for i := 0; i < num; i++ {
				ms := funcCost(f).Microseconds()
				lock.Lock()
				records = append(records, ms)

				if ms > max {
					max = ms
				}

				if min == 0 {
					min = ms
				} else {
					if ms < min {
						min = ms
					}
				}
				lock.Unlock()
			}
		}()
	}

	g.Wait()
	s := fmt.Sprintf("benchmark result microseconds:concurreny %d,num %d,all num %d,tps %f,mean %f,max %d,min %d,stdev %f", concurrency, num, len(records),
		float64(concurrency*1000*1000)/MeanInt64(records),
		MeanInt64(records), max, min, StandardDeviationInt64(records))
	return s
}

func Benchmark2(f func(int, int) bool, concurrency int, num int) string {

	g := sync.WaitGroup{}

	records := make([]int64, 0, (int64(num) * int64(concurrency)))
	max := int64(0)
	min := int64(0)
	lock := &sync.Mutex{}

	for k := 0; k < concurrency; k++ {
		g.Add(1)
		go func() {
			defer g.Done()
			for i := 0; i < num; i++ {
				var b bool
				ms := funcCost(func() {
					b = f(k, i)
				}).Microseconds()

				if !b {
					continue
				}
				lock.Lock()
				records = append(records, ms)

				if ms > max {
					max = ms
				}

				if min == 0 {
					min = ms
				} else {
					if ms < min {
						min = ms
					}
				}
				lock.Unlock()
			}
		}()
	}

	g.Wait()
	s := fmt.Sprintf("benchmark result microseconds:concurreny %d,num %d,all num %d,tps %f,mean %f,max %d,min %d,stdev %f", concurrency, num, len(records),
		float64(concurrency*1000*1000)/MeanInt64(records),
		MeanInt64(records), max, min, StandardDeviationInt64(records))
	return s
}
