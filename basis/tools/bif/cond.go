package bif

func Cond2(cond bool, yesFn func(), noFn func()) bool {
	if cond {
		yesFn()
		return true
	} else {
		noFn()
		return false
	}
}

func Cond(cond bool, yesFn func()) bool {
	if cond {
		yesFn()
		return true
	}

	return false
}

func Error(cond bool, err error) error {
	if cond {
		return err
	}
	return nil
}

func Value[T any](cond bool, first T, second T) T {
	if cond {
		return first
	}
	return second
}
