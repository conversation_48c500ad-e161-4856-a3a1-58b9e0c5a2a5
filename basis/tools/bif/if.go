package bif

type OnceIf struct {
	err error
}

func NewOnceIf() *OnceIf {
	return &OnceIf{}
}

func (p *OnceIf) If(cond bool, err error) *OnceIf {
	if p.err != nil {
		return p
	}

	Cond(cond, func() { p.err = err })
	return p
}

func (p *OnceIf) Error() error {
	if InterfaceIsNil(p.err) {
		return nil
	}
	return p.err
}

type LoopsIf struct {
	errs []error
}

func NewLoopsIf() *LoopsIf {
	return &LoopsIf{
		errs: make([]error, 0, 64),
	}
}

func (p *LoopsIf) If(cond bool, err error) *LoopsIf {
	Cond(cond, func() { p.errs = append(p.errs, err) })
	return p
}

func (p *LoopsIf) Errors() []error {
	return p.errs
}
