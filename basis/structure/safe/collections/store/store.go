package store

import "sync"

type SafeStore interface {
	GetStoreWithDef(key any, default_value any) any
	GetStore(key any) (any, bool)
	SetStore(key any, value any)
	DelStore(key any)
}

type store struct {
	store  map[any]any
	rwLock sync.RWMutex
}

func NewSafeStore() SafeStore {
	return &store{
		store:  make(map[any]any),
		rwLock: sync.RWMutex{},
	}
}

func (gc *store) GetStoreWithDef(key any, default_value any) any {
	gc.rwLock.RLock()
	defer gc.rwLock.RUnlock()
	if v, ok := gc.store[key]; ok {
		return v
	}
	return default_value
}

func (gc *store) GetStore(key any) (any, bool) {
	gc.rwLock.RLock()
	defer gc.rwLock.RUnlock()
	v, ok := gc.store[key]
	return v, ok
}

func (gc *store) SetStore(key any, value any) {
	gc.rwLock.Lock()
	defer gc.rwLock.Unlock()
	gc.store[key] = value
}

func (gc *store) DelStore(key any) {
	gc.rwLock.Lock()
	defer gc.rwLock.Unlock()
	delete(gc.store, key)
}
