package berror

import (
	"fmt"
	"slices"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"github.com/bytedance/sonic"
	"golang.org/x/exp/maps"
)

// BError 自定义错误,实现了error接口
type BError struct {
	code    int    // 错误码
	message string // 错误信息
	detail  string // 错误详情
	parents []error
	tags    map[string]any //tag
}

func NewBError(code int, msg string) *BError {
	se := &BError{code: code,
		detail:  "",
		message: msg,
		tags:    map[string]any{},
		parents: []error{}}
	return se
}

func NewBootSuccess() *BError {
	se := &BError{code: 0,
		detail:  "",
		message: "ok",
		tags:    map[string]any{},
		parents: []error{}}
	return se
}

// clone 深拷贝,并发安全
func (s *BError) clone() *BError {
	return &BError{
		code:    s.code,
		message: s.message,
		detail:  s.detail,
		parents: slices.Clone(s.parents),
		tags:    maps.Clone(s.tags),
	}
}

// Code 错误码
func (s *BError) Code() int {
	return s.code
}

func (s *BError) SetTag(k string, v any) *BError {
	s.tags[k] = v
	return s
}

func (s *BError) GetTag(k string) any {
	if bif.InterfaceIsNil(s.tags[k]) {
		return nil
	}

	return s.tags[k]
}

// Error 错误信息格式化
func (s *BError) String() string {
	parenets := []string{}

	for _, p := range s.parents {
		pb, ok := p.(*BError)
		if ok {
			//避免递归打印的影响,子级不打parents
			parenets = append(parenets, pb.String())
		} else {
			parenets = append(parenets, p.Error())
		}
	}

	sps, err := sonic.Marshal(parenets)
	var paStr string
	if err != nil {
		paStr = err.Error()
	} else {
		paStr = string(sps)
	}

	var tagStr string
	sts, err := sonic.Marshal(s.tags)
	if err != nil {
		tagStr = err.Error()
	} else {
		tagStr = string(sts)
	}

	return fmt.Sprintf("code:%d, message:%s, detail:%s, tags:%s, parents:%s", s.code, s.message, s.detail, tagStr, paStr)
}

// Derived 错误派生
func (s *BError) Derived(parent error) *BError {
	sn := s.clone()
	sn.parents = append(sn.clone().parents, parent)
	return sn
}

func (s *BError) Detaild(detail string) *BError {
	sn := s.clone()
	sn.detail = detail
	return sn
}
