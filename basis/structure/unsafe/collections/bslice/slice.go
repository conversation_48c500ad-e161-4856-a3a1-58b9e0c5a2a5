package bslice

func Reset[T any](in *[]T) {
	(*in) = (*in)[:0]
}

func Copy[T any](in []T) []T {
	dst := make([]T, len(in))

	copy(dst, in)
	return dst
}

func Delete[T comparable](s *[]T, d T) {
	for i, v := range *s {
		if v == d {
			*s = append((*s)[:i], (*s)[i+1:]...)
			break
		}
	}
}

func DeleteAll[T comparable](s *[]T, d T) {
	for i := 0; i < len(*s); {
		if (*s)[i] == d {
			*s = append((*s)[:i], (*s)[i+1:]...)
		} else {
			i++
		}
	}
}
