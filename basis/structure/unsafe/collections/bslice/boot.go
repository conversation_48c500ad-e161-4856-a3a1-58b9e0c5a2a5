package bslice

import "sort"

type BSlice[T any] struct {
	data []T
}

func NewBSliceWithData[T any](data []T) *BSlice[T] {
	return &BSlice[T]{
		data: data,
	}
}

func NewBSlice[T any]() *BSlice[T] {
	return &BSlice[T]{
		data: []T{},
	}
}

func NewBSliceWithCap[T any](cap int) *BSlice[T] {
	return &BSlice[T]{
		data: make([]T, 0, cap),
	}
}

func NewBSliceWithLen[T any](len int) *BSlice[T] {
	return &BSlice[T]{
		data: make([]T, len),
	}
}

func (b *BSlice[T]) Data() []T {
	return b.data
}

func (b *BSlice[T]) Into() *[]T {
	return &b.data
}

func (b *BSlice[T]) Filter(f func(int, T) bool) *BSlice[T] {
	results := NewBSliceWithCap[T](len(b.data))
	for i := 0; i < len(b.data); i++ {
		if f(i, b.data[i]) {
			results.data = append(results.data, b.data[i])
		}
	}
	return results
}

func (b *BSlice[T]) Map(f func(int, T) T) *BSlice[T] {
	results := NewBSliceWithCap[T](len(b.data))
	for i := 0; i < len(b.data); i++ {
		results.data = append(results.data, f(i, b.data[i]))
	}
	return results
}

func (b *BSlice[T]) Reset() *BSlice[T] {
	Reset(&b.data)
	return b
}

func (b *BSlice[T]) Copy() *BSlice[T] {
	return NewBSliceWithData(Copy(b.data))
}

func (b *BSlice[T]) Modify(f func(int, T) T) *BSlice[T] {
	for i := 0; i < len(b.data); i++ {
		b.data[i] = f(i, b.data[i])
	}

	return b
}

func (b *BSlice[T]) Delete(f func(int, T) bool) *BSlice[T] {
	for i := 0; i < len(b.data); {
		if f(i, b.data[i]) {
			b.data = append(b.data[:i], b.data[i+1:]...)
		} else {
			i++
		}
	}
	return b
}

func (b *BSlice[T]) Extend(data ...T) *BSlice[T] {
	b.data = append(b.data, data...)
	return b
}

func (b *BSlice[T]) Append(data T) *BSlice[T] {
	b.data = append(b.data, data)
	return b
}

func (b *BSlice[T]) Sort(cmp func(T, T) bool) *BSlice[T] {
	sort.Slice(b.data, func(i, j int) bool {
		return cmp(b.data[i], b.data[j])
	})
	return b
}
