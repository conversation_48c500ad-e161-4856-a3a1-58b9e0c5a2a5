package bmap

func Slice[T any, C any](input []T, slicer func(index int, item T) C) []C {
	mapped := make([]C, len(input))
	for index, item := range input {
		mapped[index] = slicer(index, item)
	}
	return mapped
}

func Map[K comparable, T any, C any](input map[K]T, mapper func(index K, item T) C) map[K]C {
	mapped := make(map[K]C, len(input))
	for index, item := range input {
		mapped[index] = mapper(index, item)
	}
	return mapped
}
