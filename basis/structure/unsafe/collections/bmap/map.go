package bmap

type BMap[K comparable, T any] struct {
	data map[K]T
}

func NewBMapWithData[K comparable, T any](data map[K]T) *BMap[K, T] {
	return &BMap[K, T]{
		data: data,
	}
}

func NewBMap[K comparable, T any]() *BMap[K, T] {
	return &BMap[K, T]{
		data: map[K]T{},
	}
}

func (b *BMap[K, T]) Data() map[K]T {
	return b.data
}

func (b *BMap[K, T]) Into() *map[K]T {
	return &b.data
}

func (b *BMap[K, T]) Filter(f func(K, T) bool) *BMap[K, T] {
	dst := NewBMap[K, T]()
	for i, _ := range b.data {
		if f(i, b.data[i]) {
			dst.data[i] = b.data[i]
		}
	}
	return dst
}

func (b *BMap[K, T]) Map(f func(K, T) T) *BMap[K, T] {
	dst := NewBMap[K, T]()
	for i, _ := range b.data {
		dst.data[i] = f(i, b.data[i])
	}
	return dst
}

func (b *BMap[K, T]) Reset() *BMap[K, T] {
	b.data = map[K]T{}
	return b
}

func (b *BMap[K, T]) Copy() *BMap[K, T] {
	dst := NewBMap[K, T]()
	for k, _ := range b.data {
		dst.data[k] = b.data[k]
	}
	return dst
}

func (b *BMap[K, T]) Modify(f func(K, T) T) *BMap[K, T] {
	for k, _ := range b.data {
		b.data[k] = f(k, b.data[k])
	}

	return b
}

func (b *BMap[K, T]) Delete(f func(K, T) bool) *BMap[K, T] {
	for k, _ := range b.data {
		if f(k, b.data[k]) {
			delete(b.data, k)
		}
	}

	return b
}

func (b *BMap[K, T]) Extend(m map[K]T) *BMap[K, T] {
	for k, _ := range m {
		b.data[k] = m[k]
	}
	return b
}
