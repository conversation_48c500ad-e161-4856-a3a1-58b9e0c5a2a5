package bcontext

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/structure/safe/collections/store"
)

type bContext struct {
	ctx context.Context
	store.SafeStore
}

type BContext interface {
	SetContext(ctx context.Context)
	Context() context.Context
	SetError(v error)
	SetName(name string)
	Name() string
	Error() error
	store.SafeStore
}

func NewBContextFromContext(ctx context.Context) BContext {
	return &bContext{
		ctx:       ctx,
		SafeStore: store.NewSafeStore(),
	}
}

func NewBContext() BContext {
	return &bContext{
		ctx:       context.TODO(),
		SafeStore: store.NewSafeStore(),
	}
}

func (gc *bContext) SetName(name string) {
	gc.SetStore(namekey{}, name)
	return
}

func (gc *bContext) Name() string {
	return gc.GetStoreWithDef(namekey{}, "").(string)
}

func (gc *bContext) SetContext(ctx context.Context) {
	gc.ctx = ctx
	return
}

func (gc *bContext) Context() context.Context {
	return gc.ctx
}

func (gc *bContext) SetError(v error) {
	gc.SetStore(errkey{}, v)
}

func (gc *bContext) Error() error {
	if v, ok := gc.GetStore(errkey{}); ok {
		return v.(error)
	}

	return nil
}
