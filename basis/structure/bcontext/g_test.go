package bcontext

import (
	"errors"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

func TestS(t *testing.T) {
	gctx := NewBContext()
	gctx.SetStore("a", 1)

	bif.Assert(gctx.GetStoreWithDef("a", nil).(int) == 1)
	bif.Assert(gctx.GetStoreWithDef("b", nil) == nil)

	gctx.SetStore("c", nil)

	bif.Assert(gctx.GetStoreWithDef("c", nil) == nil)

	bif.Assert(gctx.Error() == nil)

	gctx.SetError(errors.New("test"))

	bif.Assert(gctx.Error() != nil)

}
