2025.2.7版本：
    tlb 新增Connect函数，支持sdk 和 tlb service的创建连接。前置连接创建行为，不在Register函数调用时才创建连接;
    tlb 新增连接超时参数：ConnectTimeOutMillSecond   int `toml:"conn_timeout_mills" default:"10000"`，默认10秒超时。

2025.2.13版本：
    concurrent 支持concurrent map和slice;
    去除span中的time format，优化性能。

2025.2.17版本：
    支持func flow：函数式编程；
    支持bif：assert、when、cond、when2、cond2；
    支持boot_error：兼容标准库error，支持自定义code、error、错误码和多error 继承。

2025.2.18版本：
    支持func match：通用回调处理；
    增加bcost：通用统计代码耗时；
    pandora wrapper处理优化：post 强制解析body、get 不解析，put、delete 等其它尝试解析
    优化代码结构。

2025.2.27版本：
    elk 配置结构优化；
    支持利用goboot tomltree直接初始化自定义配置。

2025.3.4版本：
    tlb 支持多个server 注册


2025.3.14版本：
    gzip 压缩；
    sonic 替换std-json。

2025.3.17版本:v0.2.140：
    gzip 自动压缩：报文长度大于2048；可使用[compress]关闭。默认开启。

2025.3.17版本:v0.2.141：
    增加spanoption开关，控制metrics开启/关闭。

2025.3.18版本:v0.2.142：
    support mem balalst;