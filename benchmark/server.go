package main

import (
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"

	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

// request 中的payload 部分
type ReqPayload struct {
	Input string `json:"input"`
}

type RespPayload struct {
	OUT string `json:"output"`
}

var ProroAPI = pandora.NewPandoraProto[string, string]()

type spanTest struct {
	Data string
}

func process(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[string]) *pandora_proto.PandoraResponseMessage[string] {
	nspan := ctx.RootSpan().AddSpan("process")
	defer nspan.Finish()
	nspan.TraceInfo("test_data", "data")
	nspan.SetTag("xxxx", "testtag")

	nspan11 := nspan.AddSpan("nspan11")
	resp := ProroAPI.NewPandoraResponseMessage()
	resp.Payload = req.Payload
	resp.Header.Code = 111
	nspan11.Finish()

	return resp
}

func start() {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	//注册服务

	goboot.HttpServer().DefaultInstance().Router.POST("/api/v2", ProroAPI.GinWrapper().SetHandler(process).HandlerFunc())
	goboot.RunServer()

	//正式启动使用
	goboot.Singal(nil, time.Duration(1)*time.Second)
}
