package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
)

func TestMain(m *testing.M) {
	go start()

	time.Sleep(3 * time.Second)
	m.Run()
}

func TestBench(t *testing.T) {
	s := boot_bench.Benchmark2(func(k, i int) bool {

		//traceId := time.Now().UnixMilli()

		bcx := boot_tools.NewBootContext("12jkjk")

		t := "xxxx"
		//st := time.Now()
		ProroAPI.Request().
			SetAddr("0.0.0.0:50904").
			SetPayload(&t).
			SetPath("/api/v2").
			SetTraceId(fmt.Sprintf("888")).
			SetParentSpan(bcx.RootSpan().Span).
			Post(context.TODO())

		//fmt.Println("xxxxxxxxxxx", resp.Payload)
		//前10个不算，要预热
		/*
			if time.Now().Sub(st).Milliseconds() > 10 && i > 10 {
				s, _ := json.Marshal(bcx.RootSpan().JsonSpan())
				fmt.Println(string(s))
				fmt.Println(time.Now().Sub(st).Milliseconds(), traceId)
			}
		*/

		//前10个不计算
		if i < 10 {
			return false
		}

		time.Sleep(100 * time.Millisecond)
		return true
	}, 1000, 1)

	fmt.Println("test_bench", s)
}
