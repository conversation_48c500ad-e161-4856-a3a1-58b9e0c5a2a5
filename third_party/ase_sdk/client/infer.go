package aseclient

import (
	"net/http"
	"sync"
	"time"
	"fmt"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/match_context"
	asemodel "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/model"
)

// InferClient 是一个用于执行推理请求的客户端。
type InferClient struct {
	model *asemodel.AseModel
}

// NewInferClient 创建一个新的 InferClient 实例。
// model: 用于推理的模型配置。
// 返回 InferClient 实例。
func NewInferClient(model *asemodel.AseModel) *InferClient {
	return &InferClient{
		model: model,
	}
}

var (
	optimizedClient *http.Client // 优化后的HTTP客户端
	once            sync.Once    // 确保只初始化一次
)

// getHTTPClient 获取优化后的HTTP客户端单例
func getHTTPClient() *http.Client {
	once.Do(func() {
		optimizedClient = &http.Client{
			Transport: &http.Transport{
				MaxIdleConns:          200,              // 进程最大空闲连接数
				MaxIdleConnsPerHost:   100,              // 每主机最大空闲连接数
				MaxConnsPerHost:       100,              // 每主机最大连接数
				IdleConnTimeout:       90 * time.Second, // 空闲连接最大存活时间
				ResponseHeaderTimeout: 30 * time.Second, // 响应头超时时间 - ASE可能响应较慢
				TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时时间
				ExpectContinueTimeout: 2 * time.Second,  // 100-continue 超时时间
				DisableKeepAlives:     false,            // 启用keep-alive提升性能
				DisableCompression:    false,            // 启用压缩
			},
			Timeout: 0, // 使用context控制超时，不在这里设置
		}
	})
	return optimizedClient
}

// Request 创建一个新的请求实例，用于执行推理请求。
// 返回 Request 实例，包含默认的 HTTP 客户端、POST 方法和模型配置。
func (ac *InferClient) Request() *Request {
	r := &Request{
		//client 必须使用全局变量，否则不能共享连接池
		client:     getHTTPClient(),
		method:     http.MethodPost,
		timeCosts:  map[string]int64{},
		extra:      map[string]any{},
		conf:       ac.model,
		parentSpan: nil,
		header:     map[string]string{},
	}

	//设置请求策略
	r.reqStrategy = match.NewCodeMatch()
	r.reqStrategy.Match(0, func(fc *match_context.MatchContext) {
		input := fc.Get("input").(*InferReq)
		out, err := r.reqAse(fc.Context(), input)
		if err != nil {
			fc.Throw(fmt.Errorf("ASE模式请求失败: %w", err))
		}
		fc.Set("out", out)
	}).Match(1, func(fc *match_context.MatchContext) {
		input := fc.Get("input").(*InferReq)
		out, err := r.reqTlb(fc.Context(), input)
		if err != nil {
			fc.Throw(fmt.Errorf("TLB模式请求失败: %w", err))
		}
		fc.Set("out", out)
	}).Match(2, func(fc *match_context.MatchContext) {
		input := fc.Get("input").(*InferReq)
		out, err := r.reqIp(fc.Context(), input)
		if err != nil {
			fc.Throw(fmt.Errorf("本地IP模式请求失败: %w", err))
		}
		fc.Set("out", out)
	})

	return r
}
