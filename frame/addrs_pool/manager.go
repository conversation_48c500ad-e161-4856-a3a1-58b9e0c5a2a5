package addrs_pool

import (
	"sync"
)

type manager[T any] struct {
	url     string
	maxConn int
	ConnFactory[T]
	conns []*T
	index int
	lock  sync.Mutex
}

func newManager[T any](maxConn int, cf ConnFactory[T], url string) *manager[T] {
	return &manager[T]{
		conns:       make([]*T, 0, maxConn),
		maxConn:     maxConn,
		ConnFactory: cf,
		url:         url,
		index:       0,
		lock:        sync.Mutex{},
	}
}

func (m *manager[T]) NeedConnect() bool {
	m.lock.Lock()
	defer m.lock.Unlock()

	if len(m.conns) < m.maxConn {
		return true
	} else {
		return false
	}
}

func (m *manager[T]) add(conn *T) {
	m.lock.Lock()
	defer m.lock.Unlock()
	if len(m.conns) >= m.maxConn {
		return
	}

	m.conns = append(m.conns, conn)

	return
}

func (m *manager[T]) Connect() (*T, error) {
	conn, err := m.NewConnection(m.url)
	if err != nil {
		return nil, err
	}

	m.add(conn)

	return conn, nil
}

func (m *manager[T]) RRConn() *T {
	m.lock.Lock()
	defer m.lock.Unlock()

	r := m.conns[m.index]
	m.index = (m.index + 1) % len(m.conns)

	return r
}
