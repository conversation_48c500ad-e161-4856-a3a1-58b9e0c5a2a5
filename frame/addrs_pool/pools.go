package addrs_pool

import (
	"sync"
)

type AddrsPool[T any] interface {
	Get(url string) (*T, error)
	Init(urls []string) (string, error)
}

// ConnectionPool 定义连接池结构
type addrsPool[T any] struct {
	lock    sync.Mutex
	maxConn int
	ConnFactory[T]
	connsmap map[string]*manager[T]
}

// NewConnectionPool 创建一个新的连接池
func NewAddrsPool[T any](maxConn int, cf ConnFactory[T]) AddrsPool[T] {
	ap := &addrsPool[T]{
		lock:        sync.Mutex{},
		maxConn:     maxConn,
		ConnFactory: cf,
		connsmap:    map[string]*manager[T]{},
	}

	return ap
}

// Get 从连接池获取连接
func (p *addrsPool[T]) get(url string) *manager[T] {
	p.lock.Lock()
	defer p.lock.Unlock()

	_, ok := p.connsmap[url]

	if ok {
		return p.connsmap[url]
	} else {
		p.connsmap[url] = newManager(p.maxConn, p.ConnFactory, url)
		return p.connsmap[url]
	}
}

func (p *addrsPool[T]) Init(urls []string) (string, error) {
	for _, v := range urls {
		for i := 0; i < p.maxConn; i++ {
			_, err := p.Get(v)
			if err != nil {
				return v, err
			}
		}
	}

	return "", nil
}

// Get 从连接池获取连接
func (p *addrsPool[T]) Get(url string) (*T, error) {
	m := p.get(url)

	if m.NeedConnect() {
		conn, err := m.Connect()

		if err != nil {
			return nil, err
		}

		return conn, nil
	}

	return m.RRConn(), nil

}
