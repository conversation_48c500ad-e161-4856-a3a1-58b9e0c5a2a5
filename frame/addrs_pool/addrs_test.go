package addrs_pool

import (
	"fmt"
	"testing"
)

type ManticoreDao struct {
	url   string
	index int
}

type factory struct {
	index1 int
	index2 int
}

func newFactory() ConnFactory[ManticoreDao] {
	mc := &factory{
		index1: 0,
		index2: 0,
	}

	return mc
}

func (cp *factory) NewConnection(url string) (*ManticoreDao, error) {
	if url == "url1" {
		m := &ManticoreDao{
			url:   url,
			index: cp.index1,
		}
		cp.index1 += 1
		return m, nil
	}

	m := &ManticoreDao{
		url:   url,
		index: cp.index2,
	}
	cp.index2 += 1
	return m, nil
}

func TestAddrs(t *testing.T) {
	pools := NewAddrsPool(10, newFactory())

	pools.Init([]string{
		"url1",
		"url2",
	})

	for i := 0; i < 30; i++ {
		m, _ := pools.Get("url1")
		fmt.Println(i, m.url, m.index)
	}
}
