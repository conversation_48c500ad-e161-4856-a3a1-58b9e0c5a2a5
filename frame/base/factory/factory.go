package base_factory

import (
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

// Factory 是一个通用的工厂结构体，用于生成指定类型的实例
type Factory[Instance any, Model base_model.BaseModelAPI] struct {
	moduleName string // 模块名称
	tomlName   string // TOML配置文件名称
	fn         func(option *Model) (*Instance, error) // 生成实例的函数
}

// FactoryAPI 定义了工厂接口，包含创建实例、获取模块名称和TOML名称的方法
type FactoryAPI[Instance any, Model base_model.BaseModelAPI] interface {
	NewInstance(m *Model) (*Instance, error) // 创建新实例
	ModuleName() string // 获取模块名称
	TomlName() string // 获取TOML名称
}

// NewFactory 创建一个工厂实例，用于生成指定类型的实例
func NewFactory[Instance any, Model base_model.BaseModelAPI](moduleName string, tomlName string, fn func(option *Model) (*Instance, error)) FactoryAPI[Instance, Model] {
	return &Factory[Instance, Model]{
		moduleName: moduleName,
		tomlName:   tomlName,
		fn:         fn,
	}
}

// ModuleName 返回工厂的模块名称
func (f *Factory[Instance, Model]) ModuleName() string {
	return f.moduleName
}

// TomlName 返回工厂的TOML配置文件名称
func (f *Factory[Instance, Model]) TomlName() string {
	return f.tomlName
}

// NewInstance 使用工厂函数创建并返回一个新实例
func (f *Factory[Instance, Model]) NewInstance(m *Model) (*Instance, error) {
	return f.fn(m)
}