package httpserver_test

import (
	"encoding/json"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

type Test struct {
	Name  string
	Times int64
}

func TestHttplog(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	//mustlog检查适合再程序启动阶段，这样后面再使用组件是就不用再进行判空检查了。
	goboot.Logger().Must("1") //检查是否存在至少一个log实例,检查不通过会panic。
	goboot.Logger().Must("2") //检查test_logger2是否存在

	s, _ := json.Marshal(&Test{Name: "test", Times: time.Now().UnixMilli()})
	goboot.Logger().GetInstance("1").Error(string(s))
	goboot.Logger().GetInstance("2").Error(string(s))
}
