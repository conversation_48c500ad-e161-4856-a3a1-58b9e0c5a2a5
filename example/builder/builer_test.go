package builder

import (
	"fmt"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
)

type A[T any] struct {
	builder.Builder[T]
}

func (a *A[T]) CallA() *T {
	fmt.Println("this is a")
	return a.Builder.GetBuilder()
}

type B[T any] struct {
	builder.Builder[T]
}

func (b *B[T]) CallB() *T {
	fmt.Println("this is b")
	return b.Builder.GetBuilder()
}

type C struct {
	A[C]
	B[C]
}

func (c *C) CallC() *C {
	fmt.Println("this is c")
	return c
}

func TestBuilder(t *testing.T) {
	c := &C{}

	c.A.SetBuilder(c)
	c.B.SetBuilder(c)

	c.CallA().CallB().CallC()
}
