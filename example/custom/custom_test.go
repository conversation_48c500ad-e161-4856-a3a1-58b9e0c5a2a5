package custom

import (
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
	base_module "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/module"
)

const ModuleName = "custom"
const TomlName = "customs"

var G_CustomFactory = base_factory.NewFactory(ModuleName, TomlName, NewPoolInstance)

type CustomInstace struct {
	Data string
}

type CustomModel struct {
	base_model.BaseModel
	TestConf string `toml:"test_conf"`
}

func NewPoolInstance(option *CustomModel) (*CustomInstace, error) {
	return &CustomInstace{
		Data: "test_data",
	}, nil
}

var G_Custom = base_module.NewBootModule(G_CustomFactory)

func TestMultiCustom(t *testing.T) {
	err := goboot.InitFromConfig("./config")

	if err != nil {
		panic(err)
	}

	err = goboot.RegisterCustomMultiModule(G_CustomFactory)
	if err != nil {
		panic(err)
	}

	//可以生成一个module实例一直用，也可以调用Get方法生成新的实例（实例中只保存name，成本很小）
	module := goboot.GetCustomModule(G_CustomFactory)
	module.Must("custom_name")
	err = module.Need("custom_name")
	if err != nil {
		panic(err)
	}

	//或
	G_Custom.Must("custom_name")

	if goboot.GetCustomModule(G_CustomFactory).DefaultInstance().Data != "test_data" {
		panic(module.DefaultInstance().Data)
	}
}

type ReplayConf struct {
	Data struct {
		Name  string `toml:"name"`
		Age   int    `toml:"age" default:"18"`
		Email struct {
			Addrs string `toml:"addrs"`
		} `toml:"email"`
	} `toml:"userData"`
}

func TestUnmarshalConf(t *testing.T) {
	err := goboot.InitFromConfig("./config")

	if err != nil {
		panic(err)
	}

	uc := &ReplayConf{}

	err = goboot.UnmarshalConf(uc)
	if err != nil {
		panic(err)
	}

	bif.Assert(uc.Data.Name == "custom_name")
	bif.Assert(uc.Data.Age == 18)
	fmt.Println(uc.Data.Email.Addrs)
}

/*
func TestSingletonCustom(t *testing.T) {
	err := goboot.InitFromConfig("./config")

	if err != nil {
		panic(err)
	}

	err = goboot.RegisterCustomSingletonModule(G_CustomFactory)
	if err != nil {
		panic(err)
	}

	goboot.MustCustomSingletonModule(G_CustomFactory)
	err = goboot.NeedCustomSingletonModule(G_CustomFactory)
	if err != nil {
		panic(err)
	}

	if goboot.GetCustomSingletonModule(G_CustomFactory).Instance().Data != "test_data" {
		panic(goboot.GetCustomSingletonModule(G_CustomFactory).Instance().Data)
	}
}
*/
