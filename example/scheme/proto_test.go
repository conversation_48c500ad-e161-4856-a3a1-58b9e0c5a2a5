package scheme

import (
	"fmt"
	"net/url"
	"testing"
)

func TestScheme(t *testing.T) {
	n1 := "grpc://*******:8080/api/v1"
	u, err := url.Parse(n1)
	if err != nil {
		panic(err)
	}

	fmt.Println(u.Scheme, "-", u.Host)

	n2 := "grpc:dns//:*******:8080/api/v1"
	u, err = url.Parse(n2)
	if err != nil {
		panic(err)
	}

	fmt.Println(u.Scheme, "-", u.Host)

	u3, err := url.Parse(u.Host)
	if err != nil {
		panic(err)
	}

	fmt.Println(u3.Scheme, "-", u3.Host)
}
