package toml

import (
	"fmt"
	"testing"

	"github.com/pelletier/go-toml"
)

type LogModel2 struct {
	Fpath     string `toml:"file_path"`
	Level     string `toml:"level" default:"info"`
	MaxSizeMb int    `toml:"max_size_mb" default:"100"`
	MaxBackUp int    `toml:"max_back_up" default:"30"`
	MaxAge    int    `toml:"max_age_day" default:"90"`
	Compress  bool   `toml:"compress" default:"false"`
	Console   bool   `toml:"console" default:"false"`
	Options   struct {
		Format string `toml:"format"`
		MaxSize int `toml:"max_size"`
	}
}



func TestToml(t *testing.T) {
	l1,_ := toml.LoadFile("./config/1.toml")
	l2, _ := toml.LoadFile("./config/2.toml")
	l3 := l1.String() + "\n "+ l2.String()

	l4,_ := toml.LoadBytes([]byte(l3))

	v := l4.Get("loggers").([]*toml.Tree)

	logconfs := []LogModel2{}
	for _, vf := range v {
		conf := LogModel2{} 
		vf.Unmarshal(&conf)
		logconfs = append(logconfs, conf)
	}

	fmt.Println(logconfs)
}