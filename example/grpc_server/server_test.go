package grposerver_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/example/grpc_server/hellopb"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/grpc_conns"
	pandorapb "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/ppb"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type server struct {
	pandorapb.UnimplementedSkynetPandoraServiceServer
}

func (ps *server) Call(ctx context.Context, req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error) {
	meta, err := metadata.FromIncomingContext(ctx)
	fmt.Println(meta, err)
	return nil, errors.New("unsupport temporary")
}

func (ps *server) Ping(context.Context, *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error) {
	fmt.Println("ping")
	return &pandorapb.RpcResponse{}, nil
}

func start() {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	goboot.HttpServer().Must()
	goboot.GrpcServer().Must()

	pandorapb.RegisterSkynetPandoraServiceServer(goboot.GrpcServer().DefaultInstance().GenerateGrpcSrv(), &server{})
	//启动服务，启动服务前注册路由
	goboot.RunServer()

	goboot.Singal(func() {
		fmt.Println("exit")
	}, time.Duration(1)*time.Second)
}
func TestServer(m *testing.T) {
	start()

	time.Sleep(10000000 * time.Second)
}

func TestClient(m *testing.T) {
	conn, err := grpc.Dial("0.0.0.0:50905", grpc.WithInsecure())
	if err != nil {
		panic(err)
	}

	client := pandorapb.NewSkynetPandoraServiceClient(conn)
	resp, err := client.Ping(context.TODO(), &pandorapb.RpcRequest{})
	fmt.Println(resp, err)
}

/*
	func TestMain(m *testing.M) {
		go start()

		time.Sleep(10000000 * time.Second)
		m.Run()
	}
*/
func TestBenchPool(t *testing.T) {

	gpool, err := grpc_conns.NewGrpcConnPool(grpc_conns.WithTlbResolver(),
		grpc_conns.WithMaxIdleTime(30*time.Second))

	if err != nil {
		panic(err)
	}

	s := boot_bench.Benchmark2(func(k, i int) bool {
		target := goboot.BootConf().DefaultConfig().ServiceName
		rp, err := gpool.Call(context.TODO(), target, func(ctx context.Context, addr string, conn *grpc.ClientConn) (any, error) {
			client := hellopb.NewGreeterClient(conn)

			return client.SayHello(context.TODO(), &hellopb.HelloRequest{
				Name: "client test",
			})
		})

		if err != nil {
			panic(err)
		}

		if rp.(*hellopb.HelloReply).Message != "Hello client test" {
			panic("response fail" + rp.(*hellopb.HelloReply).Message)
		}
		return true
	}, 1000, 10000)
	fmt.Println("TestBenchPool", s)
}

func TestBenchOrigin(t *testing.T) {
	concurrency := 1000
	conns := make([]*grpc.ClientConn, 0, concurrency)

	for i := 0; i < 10; i++ {
		conn, err := grpc.Dial("0.0.0.0:50905", grpc.WithInsecure())
		if err != nil {
			panic(err)
		}
		conns = append(conns, conn)
	}

	s := boot_bench.Benchmark2(func(k, i int) bool {
		conn := conns[k/100]
		client := hellopb.NewGreeterClient(conn)

		rp, err := client.SayHello(context.TODO(), &hellopb.HelloRequest{
			Name: "client test",
		})
		if err != nil {
			panic(err)
		}

		if rp.Message != "Hello client test" {
			panic("response fail" + rp.Message)
		}
		return true
	}, concurrency, 10000)

	fmt.Println("TestBenchOrigin", s)
}
