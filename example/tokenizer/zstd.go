package tokenizer

import (
	"encoding/base64"
	"fmt"
	"slices"

	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/pb"
	"github.com/bytedance/sonic"
	"github.com/klauspost/compress/zstd"
)

func compressZstd(src []byte) []byte {
	var encoder, err = zstd.NewWriter(nil)
	if err != nil {
		panic(err)
	}
	return encoder.EncodeAll(src, make([]byte, 0, len(src)))
}

func depressZstd(src []byte) ([]byte, error) {

	// Create a reader that caches decompressors.
	// For this operation type we supply a nil Reader.
	var decoder, _ = zstd.NewReader(nil, zstd.WithDecoderConcurrency(0))
	return decoder.DecodeAll(src, nil)
}

func BZstd(ifer *pb.InferReq) {
	s1, _ := sonic.Marshal(ifer)

	s2 := compressZstd(s1)

	txt := base64.StdEncoding.EncodeToString(s2)
	fmt.Println("base64-zstd-compress", len(s1), len(s2), len(txt))

	bs := boot_bench.Benchmark(func() {
		compressZstd(s1)
	}, 1, 1000)

	fmt.Println("base64-zstd-encode", bs)

	s3, err := depressZstd(s2)
	bif.Assert(err == nil)

	if slices.Compare(s1, s3) != 0 {
		panic("zstd not equal")
	}

	bs = boot_bench.Benchmark(func() {
		depressZstd(s2)
	}, 1, 1000)

	fmt.Println("base64-zstd-decode", bs)
}
