package tokenizer

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"slices"

	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/pb"
	"github.com/bytedance/sonic"
)

// Function to compress data using GZIP
func CompressGZIP(data []byte) ([]byte, error) {
	var compressedData bytes.Buffer
	// Create a new GZIP writer with the default compression level
	writer := gzip.NewWriter(&compressedData)

	// Write data to the GZIP writer
	_, err := writer.Write(data)
	if err != nil {
		return nil, err
	}

	writer.Close()

	// Return the compressed data
	return compressedData.Bytes(), nil
}

// Function to decompress data using GZIP
func depressGZIP(data []byte) ([]byte, error) {
	// Create a new GZIP reader
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	reader.Close()

	// Read the decompressed data
	buf, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	return buf, nil
}

func BGzip(ifer *pb.InferReq) {
	s1, _ := sonic.Marshal(ifer)

	s2, err := CompressGZIP(s1)
	if err != nil {
		panic(err)
	}

	txt := base64.StdEncoding.EncodeToString(s2)
	fmt.Println("base64-gzip-compress", len(s1), len(s2), len(txt))

	bs := boot_bench.Benchmark(func() {
		CompressGZIP(s1)
	}, 1, 1000)

	fmt.Println("base64-gzip-encode", bs)

	s3, err := depressGZIP(s2)
	bif.Assert(err == nil)

	if slices.Compare(s1, s3) != 0 {
		panic("zstd not equal")
	}

	bs = boot_bench.Benchmark(func() {
		depressGZIP(s2)
	}, 1, 1000)

	fmt.Println("base64-gzip-decode", bs)
}
