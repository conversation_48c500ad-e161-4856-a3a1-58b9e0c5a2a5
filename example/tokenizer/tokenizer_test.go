//go:build cgo
// +build cgo

package tokenizer

import (
	"encoding/base64"
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	pandora_util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/pb"
	"github.com/bytedance/sonic"
	"github.com/golang/protobuf/proto"
)

func TestToken(t *testing.T) {
	if err := goboot.InitFromConfig("./conf.toml"); err != nil {
		panic(err)
	}

	goboot.Logger().Must()

	inst := goboot.Tokenizer().DefaultInstance()

	query := "胃管的作用是什么"
	answer := "食管的作用是通过食物，食物在口腔里咀嚼，然后通过食管进入胃部，进入下一阶段的消化和吸收。"

	queries := []string{}
	answers := []string{}
	bench := 4
	for i := 0; i < bench; i++ {
		queries = append(queries, query)
		answers = append(answers, answer)
	}

	encodings, err := inst.EncodeBatchPair(queries, answers, true)
	if err != nil {
		panic(err)
	}

	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string]*pb.Int64Slice)

	for _, encoding := range encodings {
		tokenMap["id"] = append(tokenMap["id"], pandora_util.SliceToBytes(encoding.Ids)...)
		tokenMap["mask"] = append(tokenMap["mask"], pandora_util.SliceToBytes(encoding.Masks)...)
		tokenMap["type"] = append(tokenMap["type"], pandora_util.SliceToBytes(encoding.Types)...)
	}

	batchSize := int64(len(queries))
	shapeMap["id"] = &pb.Int64Slice{
		Values: []int64{batchSize, int64(len(encodings[0].Ids))},
	}
	shapeMap["mask"] = &pb.Int64Slice{
		Values: []int64{batchSize, int64(len(encodings[0].Masks))},
	}
	shapeMap["type"] = &pb.Int64Slice{
		Values: []int64{batchSize, int64(len(encodings[0].Types))},
	}

	ifer := &pb.InferReq{
		Inputs: tokenMap,
		Shapes: shapeMap,
	}

	s, err := sonic.Marshal(ifer)

	bif.Assert(err == nil)

	txt := base64.StdEncoding.EncodeToString(s)
	fmt.Println("base64-json", len(s), len(txt))

	s, err = proto.Marshal(ifer)

	bif.Assert(err == nil)

	txt = base64.StdEncoding.EncodeToString(s)
	fmt.Println("base64-pb", len(s), len(txt))

	BZstd(ifer)

	BGzip(ifer)
}

func TestBench(t *testing.T) {
	if err := goboot.InitFromConfig("./conf.toml"); err != nil {
		panic(err)
	}

	goboot.Logger().Must()

	inst := goboot.Tokenizer().DefaultInstance()

	query := "胃管的作用是什么"
	answer := "食管的作用是通过食物，食物在口腔里咀嚼，然后通过食管进入胃部，进入下一阶段的消化和吸收。"

	queries := []string{}
	answers := []string{}
	bench := 20
	for i := 0; i < bench; i++ {
		queries = append(queries, query)
		answers = append(answers, answer)
	}

	s := boot_bench.Benchmark(func() {
		_, err := inst.EncodeBatchPair(queries, answers, true)
		if err != nil {
			panic(err)
		}
	}, 1, 1000)

	fmt.Println("TestBench:", s)
}
