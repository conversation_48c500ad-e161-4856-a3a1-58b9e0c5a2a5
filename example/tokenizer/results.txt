/*
架构：                           x86_64
CPU 运行模式：                   32-bit, 64-bit
字节序：                         Little Endian
Address sizes:                   46 bits physical, 48 bits virtual
CPU:                             32
在线 CPU 列表：                  0-31
每个核的线程数：                 2
每个座的核数：                   8
座：                             2
NUMA 节点：                      2
厂商 ID：                        GenuineIntel
CPU 系列：                       6
型号：                           45
型号名称：                       Intel(R) Xeon(R) CPU E5-2650 0 @ 2.00GHz
步进：                           7
CPU MHz：                        2399.902
CPU 最大 MHz：                   2800.0000
CPU 最小 MHz：                   1200.0000
BogoMIPS：                       4000.07
虚拟化：                         VT-x
L1d 缓存：                       512 KiB
L1i 缓存：                       512 KiB
L2 缓存：                        4 MiB
L3 缓存：                        40 MiB
NUMA 节点0 CPU：                 0,2,4,6,8,10,12,14,16,18,20,22,24,26,28,30
NUMA 节点1 CPU：                 1,3,5,7,9,11,13,15,17,19,21,23,25,27,29,31
Vulnerability Itlb multihit:     KVM: Mitigation: Split huge pages
Vulnerability L1tf:              Mitigation; PTE Inversion; VMX conditional cache flushes, SMT vulnerable
Vulnerability Mds:               Vulnerable: Clear CPU buffers attempted, no microcode; SMT vulnerable
Vulnerability Meltdown:          Mitigation; PTI
Vulnerability Spec store bypass: Mitigation; Speculative Store Bypass disabled via prctl and seccomp
Vulnerability Spectre v1:        Mitigation; Load fences, usercopy/swapgs barriers and __user pointer sanitization
Vulnerability Spectre v2:        Mitigation; Full retpoline, IBPB
Vulnerability Tsx async abort:   Not affected
标记：                           fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush dts acpi mmx fxsr sse sse2 ss ht tm pbe syscall nx pdpe1gb rdtscp lm constant_tsc 
                                 arch_perfmon pebs bts rep_good nopl xtopology nonstop_tsc aperfmperf eagerfpu pni pclmulqdq dtes64 monitor ds_cpl vmx smx est tm2 ssse3 cx16 xtpr pdcm pcid dca ss
                                 e4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx lahf_lm ssbd ibrs ibpb stibp tpr_shadow vnmi flexpriority ept vpid xsaveopt dtherm ida arat pln pts spe
                                 c_ctrl intel_stibp flush_l1d


[[tokenizers]]
enabled = true
name = "Xenova-ernie-3.0"
path = "/share/IflySearch_Resource/Prerank/0906/Xenova-ernie-3.0-nano-zh/tokenizer.json"
lib = "/share/IflySearch_Resource/ort_lib/libtokenizer_wrapper.so"
use_local = 1
use_simple = 0
pad_id = 0
pad_fixed = 0
max_length = 512
worker_num = 3
queue_num = 1024


旧版本bacthsize=20：TestBench: benchmark result microseconds:concurreny 1,num 1000,all num 1000,tps 586.977091,mean 1703.644000,max 35904,min 1220,stdev 1214.186235
裁剪协议版本 batchsize=20：TestBench: benchmark result microseconds:concurreny 1,num 1000,all num 1000,tps 707.339639,mean 1413.748000,max 34464,min 961,stdev 1153.163166