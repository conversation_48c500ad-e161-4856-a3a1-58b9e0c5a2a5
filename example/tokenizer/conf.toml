[[tokenizers]]
enabled = true
name = "Xenova-ernie-3.0"
path = "/share/IflySearch_Resource/Prerank/0906/Xenova-ernie-3.0-nano-zh/tokenizer.json"
#lib = "/share/IflySearch_Resource/libtokenizer_wrapper.so"
lib = "/share/IflySearch_Resource/libtokenizer_v1.so"
use_local = 1
use_simple = 0
pad_id = 0
pad_fixed = 0
max_length = 512
queue_num = 1024


[[loggers]]
enabled = true
name = "test_logger2"
file_path = "/datas/logs/goboot_test4.json"
console = true
