package ballast

import (
	"math/rand"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func TestBallast(t *testing.T) {
	err := goboot.InitFromConfig("./conf.toml")
	if err != nil {
		panic(err)
	}

	go allocateMemory()

	goboot.Singal(nil, 15*time.Second)
}

func allocateMemory() {
	var data [][]byte
	for {
		// 每次随机创建 1KB ~ 1MB 大小的切片
		size := rand.Intn(1024*1024) + 1024
		data = append(data, make([]byte, size))

		// 防止无限增长
		if len(data) > 1000 {
			data = nil // 清空，触发 GC
		}

		time.Sleep(10 * time.Millisecond) // 控制分配速率
	}
}
