package recycle

import (
	"fmt"
	"runtime"
	"sync"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/recycle"
)

type TsT struct {
	D string
}

var p = sync.Pool{
	New: func() any {
		return new(TsT)
	},
}

func TestRecycle(t *testing.T) {
	rp := recycle.NewRecyclePool[TsT]()

	tvalue1 := rp.Get()

	tvalue1.D = "xxxxxxxxxxxxxxxxxxx"
	rp.Release(tvalue1)

	tvalue2 := rp.Get()

	fmt.Println(tvalue2.D)
	rp.Release(tvalue2)

	runtime.GC()
	runtime.GC()

	tvalue3 := rp.Get()
	fmt.Println(tvalue3.D)
	rp.Release(tvalue3)
}
