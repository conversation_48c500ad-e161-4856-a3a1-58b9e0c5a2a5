package pandora

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

// request 中的payload 部分
type ReqPayload struct {
	Input string `json:"input"`
}

type RespPayload struct {
	OUT int `json:"output"`
}

var ProroAPI = pandora.NewPandoraProto[ReqPayload, RespPayload]()

type spanTest struct {
	Data string
}

func process(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {

	nspan := ctx.RootSpan().AddSpan("process")
	defer nspan.Finish()

	nspan2 := ctx.RootSpan().AddSpan("process2")
	defer nspan2.Finish()

	nspan.TraceInfo("test_data", "data")
	nspan.SetTag("xxxx", "testtag")
	//测试截断是否生效
	nspan.TraceInfo("test_spanst", &spanTest{
		Data: strings.Repeat("x", 1024),
	})

	nspan11 := nspan.AddSpan("nspan11")
	nspan11.TraceInfo("xx", "kl;kllk;")
	defer nspan11.Finish()

	nspan111 := nspan.AddSpan("nspan111")
	nspan111.TraceInfo("xx", "kl;c;")
	defer nspan111.Finish()

	resp := ProroAPI.NewPandoraResponseMessage()
	resp.Payload = RespPayload{
		OUT: 111,
	}
	resp.Header.Code = 111
	return resp
}

func TestPandora(t *testing.T) {
	err := goboot.InitFromConfig("./conf.toml")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	goboot.HttpServer().DefaultInstance().Router.POST("/", ProroAPI.GinWrapper().SetHandler(process).HandlerFunc())

	goboot.RunServer()

	//正式启动使用
	go goboot.Singal(nil, time.Duration(1)*time.Second)

	//测试服务使用

	time.Sleep(10 * time.Second)

	//使用tlb进行测试
	for i := 0; i < 1; i++ {
		bc := boot_tools.NewBootContext("test1")
		ProroAPI.Request().
			SetServerName(goboot.HttpServer().DefaultConfig().Name()).
			SetPayload(&ReqPayload{Input: "sss"}).
			SetPath("/").
			SetTraceId("123").
			SetParentSpan(bc.RootSpan().Span).
			Post(context.TODO())

		s, _ := json.Marshal(bc.RootSpan().JsonSpan())
		fmt.Println(string(s))
	}

	time.Sleep(3600 * time.Second)
}
