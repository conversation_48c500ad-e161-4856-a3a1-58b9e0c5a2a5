package httpserver_test

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"github.com/gin-gonic/gin"
)

func TestMain(m *testing.M) {
	go start()

	time.Sleep(3 * time.Second)
	m.Run()
}

func start() {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	goboot.HttpServer().Must()

	router := goboot.HttpServer().DefaultInstance().Router

	router.GET("/", func(ctx *gin.Context) {
		panic("test")
		ctx.String(200, "hello")

		return
	})

	//启动服务，启动服务前注册路由
	goboot.RunServer()

	goboot.Singal(func() {
		fmt.Println("222")
	}, time.Duration(1)*time.Second)
}

func TestGet(t *testing.T) {
	r, err := http.Get(fmt.Sprintf("http://0.0.0.0:%d/", goboot.BootConf().DefaultConfig().Port))
	fmt.Println(r.StatusCode, err)

	time.Sleep(60 * time.Second)
}
