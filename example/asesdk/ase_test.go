package asesdk

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
)

func TestAseSdk(t *testing.T) {
	if err := goboot.InitFromConfig("./ase.toml"); err != nil {
		panic(err)
	}

	bc := boot_tools.NewBootContext("asetest")
	goboot.AseSdk().Must() //或err = goboot.NeedRedis("redis1")

	req := &aseclient.InferReq{}
	fileByte, err := os.ReadFile("./case.json")
	if err != nil {
		panic(err)
	}

	if err := json.Unmarshal(fileByte, req); err != nil {
		panic(err)
	}

	span := bc.RootSpan().AddSpan("ase")
	defer span.Finish()

	s, err := goboot.AseSdk().DefaultInstance().Request().
		SetParentSpan(span).Send(context.TODO(), req)
	fmt.Println(s, err)
}
