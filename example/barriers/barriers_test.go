package barriers

import (
	"fmt"
	"runtime"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bcost"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

func readMem() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// fmt.Println(ms) // 太复杂了

	// 内存 通义千问
	// 打印一些关键的内存统计信息
	fmt.Printf("Alloc = %v MiB\n", m.Alloc/1024/1024)           // 分配但未释放的内存
	fmt.Printf("TotalAlloc = %v MiB\n", m.TotalAlloc/1024/1024) // 程序启动以来分配的总内存
	fmt.Printf("Sys = %v MiB\n", m.Sys/1024/1024)               // 从操作系统分配的总内存
	fmt.Printf("HeapAlloc = %v MiB\n", m.HeapAlloc/1024/1024)   // 从堆上分配但未释放的内存
	fmt.Printf("HeapSys = %v MiB\n", m.HeapSys/1024/1024)       // 由Go分配的堆内存的系统内存大小
	fmt.Printf("NumGC = %v\n", m.NumGC)                         // 进行的GC次数
}

func TestBarrier(t *testing.T) {
	err := goboot.InitFromConfig("./conf.toml")
	if err != nil {
		panic(err)
	}

	// 2. 初始化模块
	goboot.BarriersBucket().Must()

	//3个并发，100条数据
	nbb := goboot.BarriersBucket().DefaultInstance()

	bif.Assert(err == nil)

	for i := 0; i < 3; i++ {
		go func() {
			bw := nbb.Wait("test")
			time.Sleep(5 * time.Second)
			bw.Release()
		}()
	}

	fmt.Println(bcost.Cost(func() {
		time.Sleep(1 * time.Second)
		nbb.Wait(("test"))
	}).Seconds())
}
