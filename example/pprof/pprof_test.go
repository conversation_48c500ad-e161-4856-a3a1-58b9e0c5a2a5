package pprof

import (
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"github.com/gin-gonic/gin"
)

//go tool pprof -http=:8000 http://127.0.0.1:6060/debug/pprof/profile
func TestPprof(t *testing.T) {
	err := goboot.InitFromConfig("./conf.toml") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}


	goboot.BootConf().Must()
	goboot.Pprof().Must()
	goboot.HttpServer().Must()

	router := goboot.HttpServer().DefaultInstance().Router

	router.GET("/", func(ctx *gin.Context) {
		ctx.String(200, "hello")
		return
	})
	
	goboot.RunServer()
	time.Sleep(120*time.Second)
}