package tlb

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"github.com/tidwall/gjson"
)

func TestDiscover(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.TlbSdk().Enable()
	goboot.TlbSdk().Must()

	s, err := goboot.TlbSdk().DefaultInstance().GetBestService(context.TODO(), "recall-semtrc-v2")

	if err != nil {
		panic("get bestservice:" + err.Error())
	}

	fmt.Println("success------------", s)
}

func TestRegister(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	goboot.TlbSdk().Must()

	go func() {
		goboot.Singal(nil, 120*time.Second)
	}()

	time.Sleep(5 * time.Second)

	conf := goboot.TlbSdk().DefaultConfig()
	names := strings.Split(conf.ServiceName, ",")

	for _, name := range names {
		fmt.Println("name is", name)
		info, err := goboot.TlbSdk().DefaultInstance().GetBestService(context.TODO(), name)
		bif.Assert(err == nil)
		bif.Assert(info.Host == conf.Host)
		bif.Assert(info.Port == int32(conf.Port))
		bif.Assert(info.Addr() == fmt.Sprintf("%s:%d", conf.Host, conf.Port))
		fmt.Println(info.Addr())
	}

	goboot.ExitProcess()
	time.Sleep(1 * time.Second)

	for _, name := range names {
		info, err := goboot.TlbSdk().DefaultInstance().GetBestService(context.TODO(), name)
		bif.Assert(err != nil == true)
		bif.Assert(info == nil)
	}
}

func TestLic(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	goboot.TlbSdk().Must()

	go func() {
		goboot.Singal(nil, 120*time.Second)
	}()

	time.Sleep(5 * time.Second)

	conf := goboot.TlbSdk().DefaultConfig()
	names := strings.Split(conf.ServiceName, ",")

	for _, name := range names {
		info, err := goboot.TlbSdk().DefaultInstance().GetServiceInfo(context.TODO(), name)

		bif.Assert(err == nil)

		goboot.TlbSdk().DefaultInstance().UpdateMaxLic(1000)
		goboot.TlbSdk().DefaultInstance().AddUsedLic(500)

		time.Sleep(3 * time.Second)

		info, err = goboot.TlbSdk().DefaultInstance().GetServiceInfo(context.TODO(), name)

		bif.Assert(err == nil)

		maxLic := gjson.Get(info, fmt.Sprintf("%s.0.MaxLic", name)).Int()
		bif.Assert(maxLic == 1000)

		usedLic := gjson.Get(info, fmt.Sprintf("%s.0.UsedLic", name)).Int()
		bif.Assert(usedLic == 500)

		goboot.TlbSdk().DefaultInstance().DescUsedLic(500)

		time.Sleep(1 * time.Second)
		info, err = goboot.TlbSdk().DefaultInstance().GetServiceInfo(context.TODO(), name)
		bif.Assert(err == nil)

		usedLic = gjson.Get(info, fmt.Sprintf("%s.0.UsedLic", name)).Int()
		fmt.Println(info)
		bif.Assert(usedLic == 0)
	}
}

func TestGetByTag(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.TlbSdk().Must()

	go goboot.Singal(nil, time.Duration(1)*time.Second)

	time.Sleep(10 * time.Second)

	for j := 0; j < 1; j++ {
		go func() {
			for i := 0; i < 100; i++ {
				//m, err := goboot.TlbSdk().DefaultInstance().GetBestServiceEx(context.TODO(), "index-search", "A")

				m, err := goboot.TlbSdk().DefaultInstance().GetBestService(context.TODO(), "index-search")
				//m, err := goboot.TlbSdk().DefaultInstance().GetServiceInfo(context.TODO(), "index-search")
				if err != nil {
					fmt.Println("index-manager", err)
					panic(err)
				}

				fmt.Println("indexManager success", m)
			}
		}()
	}

	time.Sleep(10 * time.Second)

}
