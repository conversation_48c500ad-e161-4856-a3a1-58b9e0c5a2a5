#	Servers           string `toml:"servers"`
#	Host              string `toml:"host"`
#	Port              int    `toml:"port"`
#	Maxlics           int    `toml:"maxlics" default:"1024"`
#	HeartbeatInterval int    `toml:"heartinterval" default:"1000"`
#	CacheInterval     int    `toml:"cacheInterval" default:"1000"`
#   ConnectTimeOutMillSecond   int `toml:"conn_timeout_mills" default:"10000"`

[goboot]
enabled=true
service_name = "yyytest1,yyytest3"
port=48083

[tlb_sdk]
enabled=true
servers="*************:30131,*************:30132"
conn_timeout_mills=100