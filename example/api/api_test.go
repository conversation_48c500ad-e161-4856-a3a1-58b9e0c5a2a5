package api

import (
	"fmt"
	"net/http"
	"testing"
)

func UpdatM(m map[int64]int64) {
	m[1] = 4
}
func UpdatS(m []int64) {
	m = append(m, 1)
}

var default_http_client = http.Client{}

func TestApi(t *testing.T) {
	a := &A{
		A: 1,
	}

	var ia AFace = a

	ia.String()

	b := ia.(*A)

	b.Other()
}

type A struct {
	A int
}

func (a *A) String() {
	fmt.Println("A")
}

func (a *A) Other() {
	fmt.Println("A Other")
}

type AFace interface {
	String()
}

type B struct {
	AFace
	name string
}

func useB(b AFace) {
	c := b.(*B)
	fmt.Println(c.name)
}

func TestSlice(t *testing.T) {
	b := &B{
		name: "1",
	}
	b.AFace = &A{}
	useB(b)
}
