package funcmatch

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/match_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/match_wrapper"
)

func TestMatch(t *testing.T) {
	cm := match.NewCodeMatch()
	ctx, bRun := cm.Match("test", func(fc *match_context.MatchContext) {
		fmt.Println("test")
		fc.Set(1, 1)
	}).Match("zz", func(fc *match_context.MatchContext) {
		fmt.Println("zz")
		fc.Throw(fmt.Errorf("11"))
		fc.Set(3, 3)
	}).Catch(errors.New("zz"), func(fc *match_context.MatchContext) {
		fmt.Println("zz error")
	}).Error(func(fc *match_context.MatchContext) {
		fmt.Println("xxxxxxx", fc.Error().Error())
	}).Final(func(fc *match_context.MatchContext) {
		fc.Set(2, 2)
	}).ExcuteSimple("test")

	fmt.Println(ctx.Get(2))
	bif.Assert(ctx.Get(1) == 1)
	bif.Assert(ctx.Get(2) == 2)
	bif.Assert(ctx.Get(3) == nil)
	bif.Assert(bRun == true)

	ctx, bRun = cm.ExcuteSimple("zz")
	bif.Assert(ctx.Get(3) == 3)
	bif.Assert(bRun == true)

	_, bRun = match.NewCodeMatch().Match("test", func(fc *match_context.MatchContext) {
		fmt.Println("test")
		fc.Set(1, 1)
	}).Final(func(fc *match_context.MatchContext) {
		fc.Set(2, 2)
	}).ExcuteSimple("x")

	bif.Assert(bRun == false)

	ctx, bRun = match.NewCodeMatch().Match("test", func(fc *match_context.MatchContext) {
		fmt.Println("test")
		fc.Set(1, 1)
	}).Final(func(fc *match_context.MatchContext) {
		fc.Set(2, 2)
		panic("panic")
	}).Recover(func(fc *match_context.MatchContext, recover any) {
		fc.Throw(fmt.Errorf("%v", recover))
	}).ExcuteSimple("x")

	bif.Assert(bRun == false)
	bif.Assert(ctx.Error().Error() == "panic")
	fmt.Println(ctx.Error())

	nctx := match.NewMatchContext(context.TODO())
	nctx.Set("5", 5)
	cm.Match(3, func(fc *match_context.MatchContext) {
		fmt.Println("xxxxxxxxxxxxxxxxxxxxxxxx1", fc.Get("5"))
	})

	cm.Excute(nctx, 3)
	bif.Assert(nctx.Get("5") == 5)

	//兜底函数，当match 匹配不上时，尝试fallback兜底
	cm.FallBack(func(fc *match_context.MatchContext) {
		fmt.Println("fallback")
		fc.Throw(fmt.Errorf("fallback"))
		fc.Set(10, 10)
	})

	ctx, bRun = cm.ExcuteSimple(100)

	bif.Assert(bRun == true)
	bif.Assert(ctx.Get(10) == 10)
	bif.Assert(ctx.Error().Error() == "fallback")

	cm.Match(11, match_wrapper.CondError(true, fmt.Errorf("error")))
	result, _ := cm.ExcuteSimple(11)

	bif.Assert(result.Error().Error() == "error")
}
