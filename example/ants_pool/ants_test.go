package antspool

import (
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

func TestAnts(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.AntsPool().Must("test_ants")

	p := goboot.AntsPool().GetConfig("test_ants")

	fmt.Println(p)

	f, _ := goboot.AntsPool().DefaultInstance().Submit(func() (any, error) {
		time.Sleep(time.Duration(3) * time.Second)
		return "test", errors.New("test error")
	})

	r, ok := <-f.Result()

	if !ok {
		panic("not ok")
	}
	if r.<PERSON>().Error() != "test error" {
		panic(r.<PERSON>r())
	}

	if r.Output().(string) != "test" {
		panic(r.Output())
	}

	fmt.Println(r.Error(), r.Output())
}

func TestBench(t *testing.T) {
	err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.AntsPool().Must()

	inst := goboot.AntsPool().DefaultInstance()
	lock := sync.Mutex{}
	num := int64(0)
	count := int64(0)
	max := int64(0)

	wg := sync.WaitGroup{}
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 10000; j++ {
				st := time.Now()
				f, err := inst.Submit(func() (any, error) {
					return "test", nil
				})

				r := <-f.Result()
				e := time.Now().Sub(st).Milliseconds()

				lock.Lock()
				num += e
				count++
				if e > max {
					max = e
				}
				lock.Unlock()
				bif.Assert(err == nil)
				bif.Assert(r.Error() == nil)
				bif.Assert(r.Output().(string) == "test")
			}
		}()
	}

	wg.Wait()
	fmt.Println(max, num, count, num/count)
}
