package kafka

import (
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"github.com/Shopify/sarama"
)

func ProcessConsumer(session sarama.ConsumerGroupSession, msg *sarama.ConsumerMessage) {
	fmt.Printf("Consumed message: topic=%s partition=%d offset=%d key=%s value=%s\n",
		msg.Topic, msg.Partition, msg.Offset, string(msg.Key), string(msg.Value))

	// 标记消息为已处理（这将自动提交消息的偏移量）
	session.MarkMessage(msg, "")
}

func TestKafka(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	//组件预检，可以省去使用时的空值检查
	goboot.KafkaConsumer().Must()
	goboot.KafkaProducer().Must()

	//设置接受消息的回调
	goboot.KafkaConsumer().DefaultInstance().SetProcessor(ProcessConsumer)

	go func() {
		err := goboot.KafkaConsumer().DefaultInstance().Run()
		if err != nil {
			panic(err)
		}
	}()

	ph := goboot.KafkaProducer().GetInstance("test_pd")
	if ph == nil {
		panic("test_pd producer is nil")
	}

	partition, offset, err := ph.SyncMessage([]byte("hello"), nil, nil)
	if err != nil {
		panic(err)
	}
	fmt.Println(partition, " ", offset)
	partition, offset, err = ph.SyncMessage([]byte("hello!"), nil, nil)
	if err != nil {
		panic(err)
	}
	fmt.Println(partition, " ", offset)
	partition, offset, err = ph.SyncMessage([]byte("hello!!"), nil, nil)
	if err != nil {
		panic(err)
	}
	fmt.Println(partition, " ", offset)

	time.Sleep(10 * time.Second)
}
