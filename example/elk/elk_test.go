package test

import (
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
)

func GenSliceContents[T any](d T, n int) []T {
	r := make([]T, 0, n)
	for i := 0; i < n; i++ {
		r = append(r, d)
	}
	return r
}

func GenMapContents[T any](d T, n int) map[int]T {
	r := make(map[int]T)
	for i := 0; i < n; i++ {
		r[i] = d
	}
	return r
}

type U struct {
	Name string
	USS  []string
	Usss []int
	Mss  map[int]int
}

type spanTest struct {
	Data string
}

type pandora_span struct {
	Span span.JsonSpan `json:"span"`
}

func TestElk(t *testing.T) {

	err := goboot.InitFromConfig("./conf")
	if err != nil {
		panic(err)
	}

	//校验elk的正确性，后续使用无需判空
	goboot.BootConf().Must()
	goboot.Elklog().Must()
	goboot.Elklog().DefaultInstance().Info("traceinfo", elk.Type("testelk"), elk.SpanId("test_spanid"), elk.SubjectCode("test_appid"),
		elk.TagNumber("cost", 1.16), elk.TagString("test2", "yyyyy"),
		elk.Span(&pandora_span{}),
		elk.Data("this is test"))
	time.Sleep(3 * time.Second)
}
