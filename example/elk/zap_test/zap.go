package zap_test

import (
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func NewLogger() *zap.Logger {

	// 创建一个 JSON 编码器（你也可以使用 console 编码器）
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.LevelKey = ""
	encoderConfig.TimeKey = ""
	encoder := zapcore.NewConsoleEncoder(encoderConfig)

	errorFileWriteSyncer := zapcore.AddSync(&lumberjack.Logger{
		Filename: "./log/test.json", //日志文件存放目录
	})

	// 创建一个日志核心，将日志写入文件
	errorFileCore := zapcore.NewCore(encoder, zapcore.AddSync(errorFileWriteSyncer), zapcore.InfoLevel)
	coreArr := make([]zapcore.Core, 0, 8)
	coreArr = append(coreArr, errorFileCore)
	loggerZap := zap.New(zapcore.NewTee(coreArr...)) //zap.AddCaller()为显示文件名和行号，可省略
	return loggerZap
}
