package mongo

import (
	"context"
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"go.mongodb.org/mongo-driver/bson"
)

type Data struct {
	ID     int    `bson:"_id"`
	Entity string `bson:"entity"`
}

func TestMongo(t *testing.T) {
	err := goboot.InitFromConfig("./config")

	if err != nil {
		panic(err)
	}

	goboot.Mongo().Must("mongo1")

	// 查询所有表名
	filter := bson.M{"_id": bson.M{"$eq": 1117007023644639041}}
	var data Data
	err = goboot.Mongo().GetInstance("mongo1").Database(goboot.Mongo().GetConfig("mongo1").Dbname).Collection("baike_word").FindOne(context.Background(), filter).Decode(&data)

	if err != nil {
		panic(err)
	}

	// 打印所有表名
	fmt.Println("data:", data)

}
