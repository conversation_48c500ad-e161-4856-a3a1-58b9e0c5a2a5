package funcflow

import (
	"context"
	"errors"
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_wrapper"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/internal/console"
)

func TestCodeFlow(t *testing.T) {
	boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fmt.Println(3)
	}).Error(func(fc *flow_context.FlowContext) {
		fmt.Println(4)
		panic(fmt.Sprintf("tlb register error:%s", fc.Error().Error()))
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == 2)
		bif.Assert(fc.Get(3) == nil)
	}).ExcuteSimple()

}

func TestThrowFlow(t *testing.T) {
	ctx := boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("tlb not enable"))
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fc.Set(3, 3)
		fmt.Println(3)
	}).Error(func(fc *flow_context.FlowContext) {
		fmt.Println(4)
		panic(fmt.Sprintf("tlb register error:%s", fc.Error().Error()))
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == 2)
		bif.Assert(fc.Get(3) == 3)
	}).ExcuteSimple()

	fmt.Println("----------", ctx.Get(3), "----------")
	bif.Assert(ctx.Get(3) == 3)

	boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("error"))
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fmt.Println(3)
		fc.Set(3, 3)
	}).Error(func(fc *flow_context.FlowContext) {
		fc.Set(4, 4)
		fmt.Println(4)
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == 2)
		bif.Assert(fc.Get(3) == nil)
		bif.Assert(fc.Get(4) == 4)
	}).ExcuteSimple()
}

func TestRecoverFlow(t *testing.T) {
	boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("tlb not enable"))
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fc.Set(3, 3)
		fmt.Println(3)
	}).Error(func(fc *flow_context.FlowContext) {
		fmt.Println(4)
		panic(fmt.Sprintf("tlb register error:%s", fc.Error().Error()))
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		fc.Set(5, 5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == 2)
		bif.Assert(fc.Get(3) == 3)
		panic("test final panic")
	}).Recover(func(fc *flow_context.FlowContext, recover any) {
		fmt.Println(6, recover)
		stack := console.PanicStack()
		fmt.Println(stack)
		bif.Assert(fc.Get(5) == 5)
	}).ExcuteSimple()
}

func TestExcuteFlow(t *testing.T) {
	ctx := flow_context.NewFlowContext(context.TODO())
	ctx.Set("test", "test")
	boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
		bif.Assert(fc.Get("test").(string) == "test")
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("tlb not enable"))
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fc.Set(3, 3)
		fmt.Println(3)
	}).Error(func(fc *flow_context.FlowContext) {
		fmt.Println(4)
		panic(fmt.Sprintf("tlb register error:%s", fc.Error().Error()))
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		fc.Set(5, 5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == 2)
		bif.Assert(fc.Get(3) == 3)
	}).Excute(ctx)

	fmt.Println(ctx.Error())
}

func TestAbortFlow(t *testing.T) {
	ctx := flow_context.NewFlowContext(context.TODO())
	ctx.Set("test", "test")
	boot_flow.NewCodeFlow().Then(func(fc *flow_context.FlowContext) {
		fmt.Println(1)
		fc.Set(1, 1)
		fc.Abort()
		bif.Assert(fc.Get("test").(string) == "test")
	}).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("tlb not enable"))
	}).Catch(errors.New("tlb not enable"), func(fc *flow_context.FlowContext) {
		fc.Set(3, 3)
		fmt.Println(3)
	}).Error(func(fc *flow_context.FlowContext) {
		fmt.Println(4)
		panic(fmt.Sprintf("tlb register error:%s", fc.Error().Error()))
	}).Final(func(fc *flow_context.FlowContext) {
		fmt.Println(5)
		fc.Set(5, 5)
		bif.Assert(fc.Get(1) == 1)
		bif.Assert(fc.Get(2) == nil)
		bif.Assert(fc.Get(3) == nil)
	}).Excute(ctx)

	fmt.Println(ctx.Error(), ctx.IsAbort())
}

func TestWrapperFlow(t *testing.T) {
	ctx := flow_context.NewFlowContext(context.TODO())
	ctx.Set("test", "test")
	boot_flow.NewCodeFlow().
		Then(flow_wrapper.NeedError(goboot.Sql().Need, "xxx")).
		Then(flow_wrapper.CondError(true, errors.New("error"))).Then(func(fc *flow_context.FlowContext) {
		fmt.Println(2)
		fc.Set(2, 2)
		fc.Throw(errors.New("tlb not enable"))
	}).Excute(ctx)

	bif.Assert(ctx.Error().Error() == "error")
}
