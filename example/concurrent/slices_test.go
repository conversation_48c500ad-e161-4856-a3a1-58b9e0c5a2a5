package concurrent

import (
	"fmt"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent"
)

type Person struct {
	Name string `json:"name"`
	Age  int    `json:"age"`
}

func TestSlice(t *testing.T) {
	sle := concurrent.NewSlice[string](0,32)

	sle.UpdateFunc(func(t []string) []string{
		t = append(t, "1")
		return t
	})

	if sle.InnerCopy()[0] != "1" {
		t.<PERSON><PERSON>("TestSlice failed")
	}

	sle.Append("2")

	if sle.InnerCopy()[1] != "2" {
		t.<PERSON><PERSON><PERSON>("TestSlice failed")
	}

	sle.Append([]string{"3","4"}...)

	if sle.InnerCopy()[2] != "3" {
		t.<PERSON>rror("TestSlice failed")
	}


	sle.Range(func(i int, v string) bool {
		fmt.Println(i,v)
		return true
	})
}