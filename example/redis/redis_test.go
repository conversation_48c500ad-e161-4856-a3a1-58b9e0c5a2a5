package redis

import (
	"context"
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func TestRedis(t *testing.T) {

	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	ctx := context.Background()
	goboot.Redis().Must("redis1") //或err = goboot.NeedRedis("redis1")

	err = goboot.Redis().DefaultInstance().Set(ctx, "test1", "testdata", time.Duration(10)*time.Second).Err()

	if err != nil {
		panic(err)
	}

	r, err := goboot.Redis().DefaultInstance().Get(ctx, "test1").Result()
	if err != nil {
		panic(err)
	}

	if r != "testdata" {
		panic(goboot.Redis().DefaultInstance().Get(ctx, "test1").String())
	}

	fmt.Println(r)
}
