package evtbus

import (
	"fmt"
	"testing"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/event_bus"
)

func TestBus(t *testing.T) {
	cs, _ := event_bus.NewConsumer("cs-1", 10, 100, func(msg any) (any, error) {
		fmt.Println("test", msg)
		return "test" + msg.(string), nil
	})
	bus := event_bus.NewEvtBus()

	bif.Assert(bus.Subscribe("topic-1", cs) == nil)

	ms, err := bus.Publish("topic-1", "hello")
	bif.Assert(err == nil)

	result := <-ms["cs-1"].Result()
	bif.Assert(result.Error() == nil)
	bif.Assert(result.Output().(string) == "testhello")
}

func TestClose(t *testing.T) {
	cs, _ := event_bus.NewConsumer("cs-1", 10, 100, func(msg any) (any, error) {
		return "test" + msg.(string), nil
	})
	bus := event_bus.NewEvtBus()
	bif.Assert(bus.Subscribe("topic-1", cs) == nil)
	go func() {
		for {
			ms, _ := bus.Publish("topic-1", "hello")
			if ms != nil {
				<-ms["cs-1"].Result()
			}
		}

	}()

	time.Sleep(1 * time.Second)
	bus.Close()
	bus.Close()
	time.Sleep(3 * time.Second)
}
