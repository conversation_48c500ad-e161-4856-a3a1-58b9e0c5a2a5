package pandora

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
	pandora_client2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/client"
)

func TestMultiClient(t *testing.T) {

	g := &sync.WaitGroup{}
	for j := 0; j < 10; j++ {
		g.Add(1)
		go func() {
			defer g.Done()
			for i := 0; i < 1000; i++ {
				span := boot_tools.NewBootContext("test")
				cm := pandora_client2.NewPandoraClient[ReqPayload, RespPayload]()
				//cm.SetAddr("0.0.0.0:50904")
				cm.SetServerName("ps_test_1")
				cm.SetPath("/api/v2")
				cm.SetPayload(&ReqPayload{
					Input: "hello",
				})
				cm.SetParentSpan(span.RootSpan().Span)
				//cm.SetHeaderTag("A")
				cm.SetTraceId("123")
				f, err := cm.AsyncPost(context.TODO())

				if err != nil {
					panic(err)
				}

				resp, _ := f.Wait()

				s, _ := json.Marshal(resp)
				fmt.Println(cm.StatusCode(), string(s))

				span.RootSpan().Finish()
				js := span.RootSpan().JsonSpan()
				s, _ = json.Marshal(js)
				fmt.Println(string(s))
			}
		}()
	}

	g.Wait()
}
