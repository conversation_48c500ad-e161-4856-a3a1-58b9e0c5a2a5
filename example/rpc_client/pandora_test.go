package pandora

import (
	"fmt"
	"strings"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span/span_option"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/gin-gonic/gin"
)

// request 中的payload 部分
type ReqPayload struct {
	Input string `json:"input"`
}

type RespPayload struct {
	OUT int `json:"output"`
}

var ProroAPI = pandora.NewPandoraProto[ReqPayload, RespPayload]()

type spanTest struct {
	Data string
}

func process(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {

	nspan := ctx.RootSpan().AddSpan("process")

	defer func() {
		nspan.Finish()
		fmt.Println("process", nspan.Cost())
	}()

	nspan2 := ctx.RootSpan().AddSpan("process2", span_option.Metrics(false))
	defer func() {
		nspan2.Finish()
	}()

	time.Sleep(20 * time.Millisecond)
	nspan.TraceInfo("test_data", "data")
	nspan.SetTag("xxxx", "testtag")
	//测试截断是否生效
	nspan.TraceInfo("test_spanst", &spanTest{
		Data: strings.Repeat("x", 1024),
	})

	nspan11 := nspan.AddSpan("nspan11")
	nspan11.TraceInfo("xx", "kl;kllk;")
	defer nspan11.Finish()

	nspan111 := nspan.AddSpan("nspan111")
	nspan111.TraceInfo("xx", "kl;c;")
	defer nspan111.Finish()

	fmt.Println("input:", req.Payload.Input, req.Header.TraceId)
	resp := ProroAPI.NewPandoraResponseMessage()
	resp.Payload = RespPayload{
		OUT: 111,
	}
	resp.Header.Code = 111
	return resp
}

func TestMain(m *testing.M) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	wrapper := ProroAPI.GinWrapper()
	wrapper.SetHandler(process)

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/api/v2", wrapper.HandlerFunc())

	goboot.HttpServer().DefaultInstance().Router.GET("/", func(ctx *gin.Context) {
		ctx.String(200, ctx.Query("traceId")+ctx.Query("headerTag"))
		return
	})

	//客户端发请求
	/*
		go func() {
			time.Sleep(3 * time.Second)
			//指定地址测试
			resp, err := ProroAPI.Request().
				SetAddr("0.0.0.0:6767").
				SetPath("/api/v2").
				SetTraceId("123").
				Get(context.TODO())

			if err != nil {
				log.Fatal("api v2", err)
			}

			fmt.Println(resp.Payload.OUT, resp.Header)
		}()
	*/

	goboot.RunServer()

	//正式启动使用
	go goboot.Singal(nil, time.Duration(1)*time.Second)

	time.Sleep(3 * time.Second)

	m.Run()
}
