package cache

import (
	"fmt"
	"math/rand"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func TestCache(t *testing.T) {
	// 1. 初始化配置
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	// 2. 初始化模块
	goboot.Cache().Must()

	// 3. 测试模块
	m := make(map[string]any, 1)
	m["val"] = fmt.Sprintf("value-%d", rand.Int())
	fmt.Printf("map set val is: %s\n", m["val"])

	flag := goboot.Cache().DefaultInstance().Cache().Set("test1", m)
	if !flag {
		panic("set cache failed")
	}

	value, bool := goboot.Cache().DefaultInstance().Cache().Get("test1")
	if !bool {
		panic("get cache failed")
	}

	tmpMap := value.(map[string]any)
	fmt.Printf("map get val is: %s\n", tmpMap["val"])

	// 4. 关闭模块
	goboot.Cache().DefaultInstance().Cache().Close()
}

func TestCacheTtl(t *testing.T) {
	// 1. 初始化配置
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	// 2. 初始化模块
	goboot.Cache().Must()

	// 3. 测试模块
	m := make(map[string]any, 1)
	m["val"] = fmt.Sprintf("value-%d", rand.Int())
	fmt.Printf("map set val is: %s\n", m["val"])

	flag := goboot.Cache().DefaultInstance().Cache().SetWithTTL("test1", m, 5*time.Second)
	if !flag {
		panic("set cache failed")
	}

	time.Sleep(2 * time.Second)

	value, flag := goboot.Cache().DefaultInstance().Cache().Get("test1")
	if !flag {
		panic("get cache after 2s should be nil")
	} else {
		tmpMap := value.(map[string]any)
		fmt.Printf("map get val after 2s is: %s\n", tmpMap["val"])
	}

	time.Sleep(3 * time.Second)

	value, flag = goboot.Cache().DefaultInstance().Cache().Get("test1")
	if flag {
		tmpMap := value.(map[string]any)
		fmt.Printf("map get val after 5s is: %s\n", tmpMap["val"])
		fmt.Println("get cache after 5s should be nil")
	} else {
		fmt.Println("get cache is nil after 5s")
	}

	// 4. 关闭模块
	goboot.Cache().DefaultInstance().Cache().Close()
}

func TestCacheCost(t *testing.T) {
	// 1. 初始化配置
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	// 2. 初始化模块
	goboot.Cache().Must("cost_test")

	// 3. 测试模块

	flag := goboot.Cache().GetInstance("cost_test").Cache().SetWithCost("test1", "value", 5)
	if !flag {
		panic("set test1 failed")
	}
	value1, flag := goboot.Cache().GetInstance("cost_test").Cache().Get("test1")
	if !flag {
		fmt.Println("get test1 cache failed")
	} else {
		tmpMap := value1.(map[string]any)
		fmt.Printf("map get test1 val is: %s\n", tmpMap["val"])
	}

	flag = goboot.Cache().GetInstance("cost_test").Cache().SetWithCost("test2", "value", 5)
	if !flag {
		panic("set cache failed")
	}

	value1, flag = goboot.Cache().GetInstance("cost_test").Cache().Get("test1")
	if !flag {
		fmt.Println("get test1 cache failed")
	} else {
		tmp := value1.(string)
		fmt.Printf("map get test1 val is: %s\n", tmp)
	}

	value2, flag := goboot.Cache().GetInstance("cost_test").Cache().Get("test2")
	if !flag {
		fmt.Println("get test2 cache failed")
	} else {
		tmp := value2.(string)
		fmt.Printf("map get test2 val is: %s\n", tmp)
	}

	// 4. 关闭模块
	goboot.Cache().DefaultInstance().Cache().Close()
}

func TestCacheDel(t *testing.T) {
	// 1. 初始化配置
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	// 2. 初始化模块
	goboot.Cache().Must()

	// 3. 测试模块
	m := make(map[string]any, 1)
	m["val"] = fmt.Sprintf("value-%d", rand.Int())
	fmt.Printf("map set val is: %s\n", m["val"])

	flag := goboot.Cache().DefaultInstance().Cache().Set("test1", m)
	if !flag {
		panic("set cache failed")
	}

	value, bool := goboot.Cache().DefaultInstance().Cache().Get("test1")
	if !bool {
		panic("get cache failed")
	}

	tmpMap := value.(map[string]any)
	fmt.Printf("map get val is: %s\n", tmpMap["val"])

	goboot.Cache().DefaultInstance().Cache().Del("test1")

	_, flag = goboot.Cache().DefaultInstance().Cache().Get("test1")
	if flag {
		panic("get cache after del should be nil")
	} else {
		fmt.Println("get cache after del is nil")
	}

	// 4. 关闭模块
	goboot.Cache().DefaultInstance().Cache().Close()
}
