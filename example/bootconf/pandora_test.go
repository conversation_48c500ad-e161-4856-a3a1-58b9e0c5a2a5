package pandora

import (
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func TestPandora(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	fmt.Println(goboot.BootConf().DefaultConfig().Lables["x"].(string))
	fmt.Println(goboot.BootConf().DefaultConfig().Lables["y"].(int64))
	fmt.Println(goboot.BootConf().DefaultConfig().Lables["z"].(float64))
}
