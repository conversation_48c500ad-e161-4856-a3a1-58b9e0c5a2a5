package bootcxt

import (
	"fmt"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools"
	boot_chains "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/chains"
	boot_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"github.com/bytedance/sonic"
)

type Cstruct struct {
	Data string
}

func Testboot_tools(t *testing.T) {
	bc := boot_tools.NewBootContext("test_root")

	bc.Store().SetData("cst", &Cstruct{
		Data: "cst test",
	})

	//测试Value
	if tv1, ok := bc.Store().GetData("cst"); ok {
		tv11 := tv1.(*Cstruct)
		if tv11.Data != "cst test" {
			panic("boot ctx get value fail")
		}
	} else {
		panic("not find tv1")
	}

}

type DataTrace struct {
	Info string
}

func TestRootSpan(t *testing.T) {
	bc := boot_tools.NewBootContext("test_root")

	spag := bc.RootSpan().AddSpan("span1")

	bc.RootSpan().AddSpan("span2")

	spag.AddSpan("child span 1")

	spag.AddSpan("child span 2")

	s111 := spag.AddSpan("child span 2111")

	s111.TraceInfo("xxxx", "xxxxxxxxxxxxxxxxxxxxxx")

	bc.RootSpan().Finish()

	s, err := sonic.Marshal(bc.RootSpan().JsonSpan())

	if err != nil {
		panic(err)
	}

	fmt.Println(string(s))
}

func TestBootChains(t *testing.T) {
	engine := boot_tools.NewBootChainsEngine()

	engine.Register("test_chain", boot_chains.NewBootChainsHandler("ph1", func(bctx *boot_context.BootContext, span *span.Span) error {
		bctx.Store().SetData("test_data", &DataTrace{
			Info: "test_data",
		})
		fmt.Println("ph1", bctx)
		span.TraceInfo("ph1", "ph1 info")
		return nil
	}))

	engine.Register("test_chain", boot_chains.NewBootChainsHandler("ph2", func(bctx *boot_context.BootContext, span *span.Span) error {
		d, _ := bctx.Store().GetData("test_data")
		fmt.Println("ph2", bctx, d.(*DataTrace).Info)
		span.TraceInfo("ph2", "ph2 info")
		return nil
	}))

	btx := boot_context.NewBootContext("test_root")
	bec := boot_chains.NewContextFromBootContext(btx)
	bec.Start(engine.ChainsEngine("test_chain"))
	s, _ := sonic.Marshal(btx.RootSpan().JsonSpan())
	fmt.Println(string(s))
}
