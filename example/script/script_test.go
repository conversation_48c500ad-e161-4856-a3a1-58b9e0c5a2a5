package script

import (
	"fmt"
	"testing"

	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"github.com/dop251/goja"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
)

func TestScript(t *testing.T) {
	intp := interp.New(interp.Options{}) // 初始化一个 yaegi 解释器
	intp.Use(stdlib.Symbols)             // 允许脚本调用（几乎）所有的 Go 官方 package 代码

	intp.Eval(src) // src 就是上面的 Go 代码字符串
	v, _ := intp.Eval("plugin.Fib")
	fu := v.Interface().(func(int) int)

	s := boot_bench.Benchmark2(func(k, i int) bool {
		fu(20)

		if i < 10 {
			return false
		}

		return true
	}, 1, 100)

	fmt.Println("yaegi:", s)

	s = boot_bench.Benchmark2(func(k, i int) bool {
		Fib(20)
		if i < 10 {
			return false
		}

		return true
	}, 1, 100)

	fmt.Println("go:", s)
}

// Output:
// Fib(35) = 9227465

const src = `
package plugin

func Fib(n int) int {
	if n <= 1 {
		return n
	}
	return Fib(n-1) + Fib(n-2)
}`

func Fib(n int) int {
	if n <= 1 {
		return n
	}
	return Fib(n-1) + Fib(n-2)
}

const js = `function fib(n) {
    if (n <= 1) return n;
    return fib(n - 1) + fib(n - 2);
}`

func TestGoJa(t *testing.T) {
	vm := goja.New()
	_, err := vm.RunString(js)
	if err != nil {
		panic(err)
	}

	sum, ok := goja.AssertFunction(vm.Get("fib"))
	if !ok {
		panic("Not a function")
	}

	s := boot_bench.Benchmark2(func(k, i int) bool {
		sum(goja.Undefined(), vm.ToValue(20))

		if i < 10 {
			return false
		}

		return true
	}, 1, 100)

	fmt.Println("gojia:", s)

	s = boot_bench.Benchmark2(func(k, i int) bool {
		Fib(20)
		if i < 10 {
			return false
		}

		return true
	}, 1, 100)

	fmt.Println("go:", s)
}
