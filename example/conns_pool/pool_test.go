package connspool

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/conns_pool"
)

var g_index = 0
var lock = sync.Mutex{}

type factory struct{}

func (factory) Connect(ctx context.Context, addr string) (*TestConn, error) {
	lock.Lock()
	defer func() {
		g_index++
		lock.Unlock()
	}()

	return &TestConn{
		index: g_index,
		addr:  addr,
	}, nil
}

func (factory) Close(addr string, conn *TestConn) {
	fmt.Println(fmt.Sprintf("close-%s-%d", addr, conn.index))
	return
}

type TestConn struct {
	index int
	addr  string
}

func TestConnsPool(t *testing.T) {

	pool, err := conns_pool.NewConnsPool(&factory{}, conns_pool.WithMaxLoadNumPerConn(2), conns_pool.WithMaxConnNumPerHost(5),
		conns_pool.WithCleanInterval(1*time.Second), conns_pool.WithMaxIdleTime(5*time.Second), conns_pool.WithMaxIdleNum(2))
	if err != nil {
		panic(err)
	}

	time.Sleep(3 * time.Second)

	for i := 0; i < 10; i++ {
		go func() {
			for j := 0; j < 10; j++ {
				_, err := pool.Call(context.TODO(), "*******", func(ctx context.Context, addr string, conn *TestConn) (any, error) {
					fmt.Println(fmt.Sprintf("%s-%d", addr, conn.index))
					time.Sleep(100 * time.Millisecond)
					return fmt.Sprintf("%s-%d", addr, conn.index), nil
				})

				if err != nil {
					panic(err)
				}

			}
		}()
	}

	time.Sleep(2 * time.Second)

	for i := 0; i < 2; i++ {
		go func() {
			for j := 0; j < 10; j++ {
				_, err := pool.Call(context.TODO(), "*******", func(ctx context.Context, addr string, conn *TestConn) (any, error) {
					fmt.Println(fmt.Sprintf("%s-%d", addr, conn.index))
					time.Sleep(1000 * time.Millisecond)
					return fmt.Sprintf("%s-%d", addr, conn.index), nil
				})

				if err != nil {
					panic(err)
				}

			}
		}()
	}

	time.Sleep(5 * time.Second)

	time.Sleep(60 * time.Second)
}
