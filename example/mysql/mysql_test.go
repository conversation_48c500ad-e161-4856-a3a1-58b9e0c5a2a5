package mysql

import (
	"fmt"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func TestMysql(t *testing.T) {
	err := goboot.InitFromConfig("./config")

	if err != nil {
		panic(err)
	}

	goboot.Sql().Must("sql1")

	// 查询所有表名
	var tables []string
	err = goboot.Sql().GetInstance("sql1").Order("created_at desc").Raw("SHOW TABLES").Scan(&tables).Error

	if err != nil {
		panic(err)
	}

	// 打印所有表名
	fmt.Println("Tables in the database:", tables)
	if len(tables) == 0 {
		panic("tables len is zero")
	}

}
