package betterpool

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bcost"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

var e = func() bool {
	err := goboot.InitFromConfig("./config.toml") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
	if err != nil {
		panic(err)
	}

	goboot.BetterPool().Must()
	return true
}()

func TestPool(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)
	f, err := inst.SubmitBlock(func() (any, error) {
		return "test", nil
	})

	bif.Assert(err == nil)

	r := <-f.Result()
	bif.Assert(r.Error() == nil)
	bif.Assert(r.Output().(string) == "test")
}

func TestBlock(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	for i := 0; i < 20; i++ {
		_, err := inst.SubmitBlock(func() (any, error) {
			time.Sleep(1 * time.Second)
			return "test", nil
		})

		bif.Assert(err == nil)
	}

	f, err := inst.SubmitBlock(func() (any, error) {
		return "test", nil
	})
	bif.Assert(err == nil)

	e := bcost.Cost(func() {
		<-f.Result()
	}).Milliseconds()
	fmt.Println(e)
	bif.Assert(e >= 2000 == true)
}

func TestWithoutBlock(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	for i := 0; i < 30; i++ {
		_, err := inst.SubmitBlock(func() (any, error) {
			time.Sleep(1 * time.Second)
			return "test", nil
		})

		bif.Assert(err == nil)
	}

	_, err := inst.SubmitWithoutBlock(func() (any, error) {
		return "test", nil
	})

	fmt.Println(err.Error(), "better pool queue is full...")
}

func TestTimeOut(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	for i := 0; i < 30; i++ {
		_, err := inst.SubmitWithTimeOut(func() (any, error) {
			time.Sleep(1 * time.Second)
			return "test", nil
		}, time.Duration(100)*time.Millisecond)

		bif.Assert(err == nil || err.Error() == "submit task timeout" == true)
	}
}

func TestCtx(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	ctx, _ := context.WithTimeout(context.TODO(), 100*time.Millisecond)
	for i := 0; i < 30; i++ {
		_, err := inst.SubmitWithContext(ctx, func() (any, error) {
			time.Sleep(1 * time.Second)
			return "test", nil
		})

		bif.Assert(err == nil || err.Error() == "context deadline exceeded" == true)
	}
}

func TestClose(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	go func() {
		for {
			_, err := inst.SubmitBlock(func() (any, error) {
				return "test", nil
			})

			bif.Assert(err == nil || err.Error() == "pool closed" == true)
		}
	}()

	time.Sleep(1 * time.Second)
	inst.Close()
	time.Sleep(3 * time.Second)
}

func TestBench(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()

	lock := sync.Mutex{}
	num := int64(0)
	count := int64(0)
	max := int64(0)

	wg := sync.WaitGroup{}
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 10000; j++ {
				st := time.Now()
				f, err := inst.SubmitBlock(func() (any, error) {
					return "test", nil
				})

				r := <-f.Result()
				e := time.Now().Sub(st).Milliseconds()

				lock.Lock()
				num += e
				count++
				if e > max {
					max = e
				}
				lock.Unlock()
				bif.Assert(err == nil == true)
				bif.Assert(r.Error() == nil)
				bif.Assert(r.Output().(string) == "test")
			}
		}()
	}

	wg.Wait()
	fmt.Println(max, num, count, num/count)
}

func TestPanic(t *testing.T) {
	inst := goboot.BetterPool().DefaultInstance()
	conf := goboot.BetterPool().DefaultConfig()
	bif.Assert(conf.ExpireMs == 2000)
	bif.Assert(conf.PreAlloc == true)
	bif.Assert(conf.QueueNum == 10)
	bif.Assert(conf.MaxConcurrent == 10)

	lock := sync.Mutex{}
	num := int64(0)
	count := int64(0)

	for i := 0; i < 10; i++ {
		go func() {
			for {
				st := time.Now().UnixMilli()
				_, err := inst.SubmitBlock(func() (any, error) {
					e := time.Now().UnixMilli() - st
					lock.Lock()
					num += e
					count++
					lock.Unlock()
					time.Sleep(100 * time.Millisecond)
					panic("test")

					return "test", nil
				})

				bif.Assert(err == nil || err.Error() == "pool closed" == true)

			}
		}()
	}

	time.Sleep(3 * time.Second)

	fmt.Println(num, count, num/count)
}
