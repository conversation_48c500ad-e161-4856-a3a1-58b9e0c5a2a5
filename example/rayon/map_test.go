package rayon

import (
	"context"
	"fmt"
	"runtime"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/rayon"
)

func calmap(ctx context.Context, data map[int]int) (map[int]string, error) {

	outs := map[int]string{}

	for i, v := range data {
		outs[i] = fmt.Sprintf("%d", v)
	}

	return outs, nil
}

func calmap2(ctx context.Context, data map[int]int) (map[int]string, error) {
	outs := map[int]string{}

	for i, v := range data {
		outs[i] = fmt.Sprintf("%d", v)
	}
	all := 0
	for i := 0; i < 10000000000000; i++ {
		all += 1
	}
	return outs, nil
}

func createMap(j int) map[int]int {

	fmt.Println("create map", j)
	outs := map[int]int{}

	for i := 0; i < j; i++ {
		outs[i] = i
	}
	return outs
}

func TestMap(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	fmt.Println(runtime.NumCPU(), goboot.Rayon().DefaultConfig().Parallm, goboot.Rayon().DefaultConfig().MinChunckSize)

	if !goboot.Rayon().Enable() {
		panic("not enable")
	}

	inputs := createMap(0)

	outs, err := rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(1)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(30)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(31)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(48)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(61)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(62)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)

	inputs = createMap(80)

	outs, err = rayon.Map(context.TODO(), inputs, calmap)
	fmt.Println(outs, err)
}
