package rayon

import (
	"context"
	"fmt"
	"runtime"
	"testing"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/rayon"
)

func cal(ctx context.Context, data []int) ([]string, error) {
	//fmt.Println(len(data))
	outs := make([]string, 0, len(data))

	for _, v := range data {
		outs = append(outs, fmt.Sprintf("%d", v))
	}

	return outs, nil
}

func cal2(ctx context.Context, data []int) ([]string, error) {
	outs := make([]string, 0, len(data))

	for _, v := range data {
		outs = append(outs, fmt.Sprintf("%d", v))
	}

	all := 0
	for i := 0; i < 10000000000000; i++ {
		all += 1
	}

	return outs, nil
}

func createSlice(j int) []int {

	fmt.Println("create slice", j)
	o := make([]int, 0, j)
	for i := 0; i < j; i++ {
		o = append(o, i)
	}
	return o
}

func TestSlice(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	fmt.Println(runtime.NumCPU(), goboot.Rayon().DefaultConfig().Parallm, goboot.Rayon().DefaultConfig().MinChunckSize)

	if !goboot.Rayon().Enable() {
		panic("not enable")
	}

	inputs := createSlice(0)

	outs, err := rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(1)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(30)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(31)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(32)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(48)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(61)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(62)

	outs, err = rayon.Slice(context.TODO(), inputs, cal)
	fmt.Println(outs, err)

	inputs = createSlice(80)

	outs, err = rayon.Slice(context.TODO(), inputs, cal2)
	fmt.Println(outs, err)
}
