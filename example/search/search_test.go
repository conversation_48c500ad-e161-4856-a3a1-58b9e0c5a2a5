package search_sdk

import (
	"context"
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	boot_bench "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bench"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/search_sdk/config"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/search_sdk/search"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/search_sdk/session"
)

func TestSearch(t *testing.T) {

	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.SearchSdk().Must()

	s := goboot.SearchSdk().DefaultInstance()

	authInfo, err := s.Auth().SetTraceId(time.Now().String()).RequestAuth(context.TODO())

	//s1,_ := sonic.Marshal(auth)
	//fmt.Println(string(s1))
	if err != nil {
		panic("auth:" + err.Error())
	}

	/*
		        // 搜索业务参数
				"query": [
					"牙周炎"
				],
				"scene": ["诊疗-治疗方案-检查检验治疗方案","诊疗-治疗方案-其他"]
				"intent":"5001", //搜索意图id，业务侧请联系产品经理提前获取
				"filter":{
					"filedName":"post_ts", //检索字段
					"startTime":123456, //秒
					"endTime":123456, //秒
					"domain":["sina.com.cn"]
				},

	*/

	health_payload := &search.SearchRequestPayload{
		Query: []string{"牙周炎"},
		Scene: []string{"诊疗-治疗方案-其他"},
	}

	resp, err := s.Search().
		SetAuthInfo(authInfo).
		SetPayload(health_payload).
		SetContext(context.TODO()).
		SetUid("123").
		RequestSearch()
	fmt.Println("search:", string(resp))
}

func TestSession(t *testing.T) {

	//加载配置启动
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.SearchSdk().Must()

	new_session, _ := goboot.SearchSdk().DefaultInstance().Session()

	//也可以不加载配置启动,直接指定配置：
	new_session2, _ := session.NewSession(&config.SearchModel{
		AppId:      "cc501f15",
		AppKey:     "cc501f15",
		AppSecret:  "11E9750C72E849F28FA8ED00",
		ProdCode:   "Search",
		RouterUrl:  "http://10.103.240.171:30013/v1/route",
		EnvType:    2,
		Idc:        "hf",
		ExpireSecs: 3,
	})

	bif.Assert(new_session2 == nil == false)

	health_payload := &search.SearchRequestPayload{
		Query: []string{"牙周炎"},
		Scene: []string{"诊疗-治疗方案-其他"},
	}

	//5路，1000次并发测试
	bench_result := boot_bench.Benchmark(func() {
		new_search, err := new_session.NewSearch(context.Background())

		if err != nil {
			panic(err)
		}

		//创新新的requesst 运行时，发起请求，自动继承了上面的context
		//自动维护auth 信息。
		_, err = new_search.SetPayload(health_payload).RequestSearch()

		if err != nil {
			panic(err)
		}

		//fmt.Println("search:", string(resp))
	}, 50, 1000)

	fmt.Println("bench_result", bench_result)
}
