[goboot]
enabled=true
service_name="ps_test_2"
port=50909

#server 配置
[[http_server]]
enabled=true

[http_server.metrics]
url="/actuator/prometheus"

#tlb配置
[tlb_sdk]
enabled=true
servers="**************:30132,**************:30132"

[compress]
enabled=true
compress_len=0

#默认日志
[[loggers]]
enabled=true
pure=true
file_path="./logs/goboot_test.json"

[elk]
enabled=true
name="elk_log"
log_level="info"
tags="region:hf,env:test"


[elk.kafka]
brokers = "kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"
topic = "lynxiao_flow"

