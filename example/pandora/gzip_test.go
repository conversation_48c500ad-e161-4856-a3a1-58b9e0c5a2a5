package pandora

import (
	"context"
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

func processGzip(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {
	fmt.Println(ctx.GetGinCtx().Request.Header)
	nspan := ctx.RootSpan().AddSpan("process")
	defer nspan.Finish()

	fmt.Println("xxxxxxxxxxx:", req.Payload.Input)
	resp := ProroAPI.NewPandoraResponseMessage()
	resp.Payload = RespPayload{
		OUT: 111,
	}
	return resp
}

type ReqPayload2 struct {
	Empty           bool                   `json:"empty"`
	AdditionalProp1 map[string]interface{} `json:"additionalProp1"`
}

var ProroAPI2 = pandora.NewPandoraProto[ReqPayload2, ReqPayload2]()

func TestGzip(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	wrapper := ProroAPI.GinWrapper()
	wrapper.SetHandler(processGzip)

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/", wrapper.HandlerFunc())
	goboot.RunServer()

	//正式启动使用
	goboot.Singal(nil, time.Duration(1)*time.Second)
}

func TestClientGzip(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	resp1, err := ProroAPI2.Request().
		SetAddr("*********:31600").
		SetPayload(&ReqPayload2{Empty: false, AdditionalProp1: map[string]interface{}{
			"1": "1",
		}}).
		SetPath("/post").
		Post(context.TODO())

	fmt.Println(resp1, err)
}
