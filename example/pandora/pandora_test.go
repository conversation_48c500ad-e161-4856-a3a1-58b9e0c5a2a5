package pandora

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span/span_option"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
)

// request 中的payload 部分
type ReqPayload struct {
	Input string `json:"input"`
}

type RespPayload struct {
	OUT int `json:"output"`
}

var ProroAPI = pandora.NewPandoraProto[ReqPayload, RespPayload]()

type spanTest struct {
	Data string
}

func process(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {
	nspan := ctx.RootSpan().AddSpan("process")

	defer func() {
		nspan.Finish()
	}()

	nspan2 := ctx.RootSpan().AddSpan("process2", span_option.Metrics(false))
	defer func() {
		nspan2.Finish()
	}()

	nspan.TraceInfo("test_data", "data")
	nspan.SetTag("xxxx", "testtag")
	//测试截断是否生效
	nspan.TraceInfo("test_spanst", &spanTest{
		Data: strings.Repeat("x", 1024),
	})

	nspan11 := nspan.AddSpan("nspan11")
	nspan11.TraceInfo("xx", "kl;kllk;")
	defer nspan11.Finish()

	nspan111 := nspan.AddSpan("nspan111")
	nspan111.TraceInfo("xx", "kl;c;")
	defer nspan111.Finish()

	resp := ProroAPI.NewPandoraResponseMessage()
	resp.Payload = RespPayload{
		OUT: 111,
	}
	resp.Header.Code = 111
	return resp
}

func processMiddle(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {

	fmt.Println("root span test", ctx.RootSpan().Uri, ctx.RootSpan().Method, ctx.RootSpan().Path)
	fmt.Println(ctx.RootSpan().Uri)
	nspan := ctx.RootSpan().AddSpan("9999")
	resp := ProroAPI.NewPandoraResponseMessage()
	nspan.Run(func() {
		resp1, err := ProroAPI.Request().
			SetServerName(goboot.HttpServer().DefaultConfig().Name()).
			SetPayload(&ReqPayload{Input: "sss"}).
			SetPath("/api/v2").
			SetParentSpan(nspan).
			SetTraceId(req.Header.TraceId).
			SetHeaderTag(req.Header.Tag).
			Post(context.TODO())
		if err != nil {
			nspan.TraceInfo("err", err)
			fmt.Println("panic", err.Error())
			return
		}

		ProroAPI.Request().
			SetAddr("0.0.0.0:50909").
			//SetServerName(goboot.BootConf().DefaultConfig().ServiceName).
			SetPayload(&ReqPayload{Input: "sss"}).
			SetPath("/api/v2").
			SetParentSpan(nspan).
			SetTraceId(req.Header.TraceId).
			Post(context.TODO())

		resp.Header.Code = resp1.Header.Code

		resp.Payload = resp1.Payload
	})

	fmt.Println("middle", resp)

	return resp
}

func cus_unmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[ReqPayload]) error {

	fmt.Println("this is custom unmarshal")
	return sonic.Unmarshal(out, req)
}

func TestPandora(t *testing.T) {

	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	wrapper := ProroAPI.GinWrapper()
	wrapper.SetHandler(processMiddle)

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/", wrapper.HandlerFunc())

	goboot.HttpServer().DefaultInstance().Router.POST("/api/v2", ProroAPI.GinWrapper().SetHandler(process).SetRequestUnmarshal(cus_unmarshal).HandlerFunc())

	//客户端发请求
	/*
		go func() {
			time.Sleep(3 * time.Second)
			//指定地址测试
			resp, err := ProroAPI.Request().
				SetAddr("0.0.0.0:6767").
				SetPath("/api/v2").
				SetTraceId("123").
				Get(context.TODO())

			if err != nil {
				log.Fatal("api v2", err)
			}

			fmt.Println(resp.Payload.OUT, resp.Header)
		}()
	*/

	goboot.RunServer()

	//正式启动使用
	go goboot.Singal(nil, time.Duration(1)*time.Second)

	//测试服务使用

	time.Sleep(3 * time.Second)

	//使用tlb进行测试
	for i := 0; i < 1; i++ {
		fmt.Println(i)
		resp, _ := ProroAPI.Request().
			//SetServerName(goboot.HttpServer().DefaultConfig().Name()).
			SetAddr("0.0.0.0:50909").
			SetPayload(&ReqPayload{Input: "sss"}).
			SetPath("/").
			SetTraceId("1233").
			SetQuery("xx", "yy").
			Post(context.TODO())

		s, _ := sonic.Marshal(resp)

		fmt.Println("resp succes", string(s))
	}

	time.Sleep(3600 * time.Second)
}
