package pandora

import (
	"context"
	"fmt"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
)

func grpcMiddle(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[ReqPayload]) *pandora_proto.PandoraResponseMessage[RespPayload] {
	fmt.Println("uri", ctx.RootSpan().Uri)
	fmt.Println("path", ctx.RootSpan().Path)
	fmt.Println("method", ctx.RootSpan().Method)
	fmt.Println("server", ctx.RootSpan().ServerName)
	nspan := ctx.RootSpan().AddSpan("process")
	defer nspan.Finish()
	resp := ProroAPI.NewPandoraResponseMessage()
	resp1, _ := ProroAPI.Request().
		SetServerName(goboot.HttpServer().DefaultConfig().Name()).
		SetPayload(&ReqPayload{Input: "sss"}).
		SetPath("/api/v2").
		SetParentSpan(nspan).
		SetTraceId(req.Header.TraceId).
		SetHeaderTag(req.Header.Tag).
		Grpc(context.TODO())

	ProroAPI.Request().
		SetAddr("0.0.0.0:50904").
		//SetServerName(goboot.BootConf().DefaultConfig().ServiceName).
		SetPayload(&ReqPayload{Input: "sss"}).
		SetPath("/api/v2").
		SetParentSpan(nspan).
		SetTraceId(req.Header.TraceId).
		Grpc(context.TODO())

	resp.Header.Code = resp1.Header.Code
	resp.Payload = resp1.Payload
	return resp
}

func TestGrpc(t *testing.T) {
	err := goboot.InitFromConfig("./grpc_conf")
	if err != nil {
		panic(err)
	}

	goboot.BootConf().Must()
	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	goboot.TlbSdk().Must()

	wrapper := ProroAPI.GrpcWrapper()
	wrapper.SetHandler("/", grpcMiddle)
	wrapper.SetHandler("/api/v2", process)

	//注册服务

	goboot.GrpcServer().DefaultInstance().RegisterService(wrapper.HandlerFunc())

	//客户端发请求
	/*
		go func() {
			time.Sleep(3 * time.Second)
			//指定地址测试
			resp, err := ProroAPI.Request().
				SetAddr("0.0.0.0:6767").
				SetPath("/api/v2").
				SetTraceId("123").
				Get(context.TODO())

			if err != nil {
				log.Fatal("api v2", err)
			}

			fmt.Println(resp.Payload.OUT, resp.Header)
		}()
	*/

	goboot.RunServer()

	//正式启动使用
	go goboot.Singal(nil, time.Duration(1)*time.Second)

	//测试服务使用

	time.Sleep(10 * time.Second)

	//使用tlb进行测试
	for i := 0; i < 3; i++ {
		resp, _ := ProroAPI.Request().
			SetServerName(goboot.HttpServer().DefaultConfig().Name()).
			SetPayload(&ReqPayload{Input: "sss"}).
			SetPath("/").
			SetTraceId("1233").
			SetQuery("xx", "yy").
			Grpc(context.TODO())

		s, _ := sonic.Marshal(resp)

		fmt.Println(string(s))
	}

	time.Sleep(3600 * time.Second)
}
