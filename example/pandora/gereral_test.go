package pandora

import (
	"context"
	"fmt"
	"log"
	"testing"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_general "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/general"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
)

func GeneralProcess(ctx *pandora_context.PandoraContext, req *pandora_general.GeneralRequest) *pandora_general.GeneralRespone {

	fmt.Println("input is", req.Payload.(ReqPayload).Input)
	resp := pandora_general.NewGeneralResponseMessage()
	resp.Payload = RespPayload{
		OUT: 111,
	}
	return resp
}

func GeneralDELProcess(ctx *pandora_context.PandoraContext, req *pandora_general.GeneralRequest) *pandora_general.GeneralRespone {
	taskId := ctx.GetGinCtx().DefaultQuery("taskId", "")
	fmt.Println("taskId is", taskId)
	resp := pandora_general.NewGeneralResponseMessage()
	resp.Payload = RespPayload{
		OUT: 0,
	}
	return resp
}

func cus_unmarshal2(out []byte, req *pandora_proto.PandoraRequestMessage[ReqPayload]) error {

	fmt.Println("this is custom genranl unmarshal")
	return sonic.Unmarshal(out, req)
}

func TestGeneral(t *testing.T) {
	err := goboot.InitFromConfig("./config")
	if err != nil {
		panic(err)
	}

	//检查elklog 是否正确加载
	goboot.Elklog().Must()

	//检查http server 是否正确加载
	goboot.HttpServer().Must()

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("", pandora_general.Pandora_General.GinWrapper().SetHandler(GeneralProcess).
		SetRequestUnmarshal(pandora_general.GeneralRequestUnmarshal(pandora_general.GeneralRequestUnmarshal[ReqPayload](cus_unmarshal2))).HandlerFunc())

	goboot.HttpServer().DefaultInstance().Router.POST("/api/v2", pandora_general.Pandora_General.GinWrapper().SetHandler(GeneralProcess).
		SetRequestUnmarshal(pandora_general.GeneralRequestUnmarshal(pandora_general.GeneralRequestUnmarshal[ReqPayload](cus_unmarshal2))).
		HandlerFunc())

	goboot.HttpServer().DefaultInstance().Router.DELETE("/api/v2/del", pandora_general.Pandora_General.GinWrapper().SetHandler(GeneralDELProcess).HandlerFunc())

	goboot.RunServer()

	//测试服务使用

	time.Sleep(3 * time.Minute)

	//使用tlb进行测试
	resp, _ := ProroAPI.Request().
		SetServerName("pandora_test").
		SetPayload(&ReqPayload{Input: "sss"}).
		//SetPath("/").
		SetTraceId("123").
		Post(context.TODO())

	if err != nil {
		log.Fatal("/", err)
	}

	fmt.Println("/", resp.Payload.OUT, resp.Header)

	s, _ := sonic.Marshal(resp)

	fmt.Println(string(s))

	//使用tlb进行测试
	resp, _ = ProroAPI.Request().
		SetServerName("pandora_test").
		SetPayload(&ReqPayload{Input: "sss"}).
		SetPath("/api/v2").
		SetTraceId("123").
		Post(context.TODO())

	if err != nil {
		log.Fatal("/api/v2", err)
	}

	fmt.Println("/", resp.Payload.OUT, resp.Header)

	s, _ = sonic.Marshal(resp)

	fmt.Println(string(s))
}
