package main

/*
#cgo LDFLAGS : -ldl
#include <dlfcn.h>
#include <stdlib.h>
//#创建加密狗
//Create_Dongle参数传入的是：授权文件和日志文件的路径
typedef int (*CreateDongleFunc)(const char*);
int CallCreateDongle(void* f, const char* path) {
    return ((CreateDongleFunc)f)(path);
}

// Init_AuthMgr 参数是：产品名、版本号，以及最后的 authorcode，若 hasp 授权则传空字符串
typedef int (*InitAuthMgrFunc)(const char*, int, int, int, const char*);
int CallInitAuthMgr(void* f, const char* name, int major, int minor, int patch, const char* authorcode) {
    return ((InitAuthMgrFunc)f)(name, major, minor, patch, authorcode);
}

// SetErrorCB，方式一：由加密狗内部定时检测
typedef int (*SetErrorCBFunc)(int, int);
int CallSetErrorCB(void* f, int interval, int maxFail) {
    return ((SetErrorCBFunc)f)(interval, maxFail);
}

// CheckDongle，方式二：由外部主动周期调用
typedef int (*CheckDongleFunc)();
int CallCheckDongle(void* f) {
    return ((CheckDongleFunc)f)();
}
*/
import "C"
import (
	"fmt"
	"unsafe"
)

// 全局句柄和函数指针
var (
	libHandle    unsafe.Pointer
	createDongle unsafe.Pointer
	initAuthMgr  unsafe.Pointer
	setErrorCB   unsafe.Pointer
	checkDongle  unsafe.Pointer
)

// 加载动态库
func LoadLibrary(libPath string) {
	cPath := C.CString(libPath)
	defer C.free(unsafe.Pointer(cPath))

	libHandle = C.dlopen(cPath, C.RTLD_NOW)
	if libHandle == nil {
		err := C.GoString(C.dlerror())
		panic("failed to open library: " + err)
	}
}

// 加载函数符号
func LoadSymbol(name string) unsafe.Pointer {
	cName := C.CString(name)
	defer C.free(unsafe.Pointer(cName))

	sym := C.dlsym(libHandle, cName)
	if sym == nil {
		err := C.GoString(C.dlerror())
		panic("failed to load symbol " + name + ": " + err)
	}
	return sym
}

// 加载所有函数
func LoadDongleFuncs() {
	createDongle = LoadSymbol("Create_Dongle")
	initAuthMgr = LoadSymbol("Init_AuthMgr")
	setErrorCB = LoadSymbol("Set_ErrorCB")
	checkDongle = LoadSymbol("Check_Dongle")
}

func CreateDongle(path string) int {
	cPath := C.CString(path)
	defer C.free(unsafe.Pointer(cPath))
	return int(C.CallCreateDongle(createDongle, cPath))
}

func InitAuthMgr(name string, major, minor, patch int, authcode string) int {
	cName := C.CString(name)
	cAuth := C.CString(authcode)
	defer C.free(unsafe.Pointer(cName))
	defer C.free(unsafe.Pointer(cAuth))
	return int(C.CallInitAuthMgr(initAuthMgr, cName, C.int(major), C.int(minor), C.int(patch), cAuth))
}

func SetErrorCB(period, maxFailures int) int {
	return int(C.CallSetErrorCB(setErrorCB, C.int(period), C.int(maxFailures)))
}

func CheckDongle() int {
	return int(C.CallCheckDongle(checkDongle))
}

func main() {
	LoadLibrary("/opt/jfs/lynxiao-deploy/lynxiao/auth_demo/python-SDK/libdongle.so")
	LoadDongleFuncs()
	CreateDongle("/opt/jfs/lynxiao-deploy/lynxiao/dongle")

	InitAuthMgr("Lynxiao", 2, 1, 0, "")
	ret := CheckDongle()
	fmt.Println(ret)
}
