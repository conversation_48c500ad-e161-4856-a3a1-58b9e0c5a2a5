#-*- coding:UTF-8 -*-
from ctypes import *
import time

#创建加密狗
#Create_Dongle参数传入的是：授权文件和日志文件的路径
lib = CDLL("./libdongle.so")
print("Create_Dongle ", lib.Create_Dongle(b"../../dongle"))


#初始化
##Init_AuthMgr 参数是：产品名、版本号，以及最后的authorcode，若hasp授权则传空字符串，若hasp_authorcode 授权，则传入  b"引擎的authorcode码"
name = b"Lynxiao"
print("InitAuthMgr ", lib.Init_AuthMgr(name, 2, 1, 0, "" ))


##下面是可供调用的接口，通过接口获取授权信息，按需调用
#获取许可次数
print("Get_Licence ", lib.Get_Licence())
#获取音库
lib.Get_Voices.restype = c_char_p
print("Get_Voices ", lib.Get_Voices())
#获取授权引擎名
lib.Get_ProdName.restype = c_char_p
print("Get_ProdName ", lib.Get_ProdName())
#获取资源包
lib.Get_Packages.restype = c_char_p
print("Get_Packages ", lib.Get_Packages())
#获取产品版本信息
lib.Get_ProdVersion.restype = c_char_p
print("Get_ProdVersion ", lib.Get_ProdVersion())
#获取过期时间
lib.Get_ExpiredTime.restype = c_char_p
print("Get_ExpiredTime ", lib.Get_ExpiredTime())

#方式一：加密狗自己开启线程，调用检测函数，传入检测周期，和允许失败次数
#print("Set_ErrorCB ", lib.Set_ErrorCB(5, 20))

#方式：周期调用checkdongle接口
for i in range(1, 5):
   print("ret : ", lib.Check_Dongle())
   time.sleep(2)
   print(i)
time.sleep(10)