import com.iflytek.idg;

public class demo {

	/**
	 * @param args
	 */
	public static void main(String[] args) {

	
		new Thread(new Runnable(){
		public void run(){
		idg obj = new idg();
		long ret = obj.jinit("iFLY_AUTH", 1, 0, 0, "", "D:/Code/Dongle/dongleCenterAuth-master/lib/log", "D:/Code/Dongle/dongleCenterAuth-master/lib/res");
		System.out.println("iFLY_AUTH 1.0.0");
		if (ret == 0)
		{
			System.out.println("Dongle init OK!");
		}
		else
		{
			System.out.println("Dongle init failed!");
		}
		
		long lce = obj.jget_licence();
		System.out.println("Dongle licence is " + lce);
		
		/*ret = obj.jfini();
		if (ret == 0)
		{
			System.out.println("Dongle fini OK!");
		}
		else
		{
			System.out.println("Dongle fini failed!");
		}*/
		long retn;
		while(true){
			retn = obj.jdongle_check();
			System.out.println("retn : " +retn);
			try{
				Thread.sleep(1000);
			}catch(InterruptedException  e){
				e.printStackTrace();
			}
		}	
	}}).start();
	}
	


}