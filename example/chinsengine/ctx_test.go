package chains_engine

import (
	"errors"
	"fmt"
	"testing"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
)

func ph1(ctx chains_engine.ChainsContext) {
	fmt.Println("1 start")
	//ctx.Abort()
	fmt.Println("1 end")
}

func ph2(ctx chains_engine.ChainsContext) {
	fmt.Println("2 start")
	//mysql

	//处理上半

	//执行数据库操作，请求下游
	ctx.Next() //执行colletcions

	//处理下半

	fmt.Println("2 end")
}

func ph3(ctx chains_engine.ChainsContext) {
	fmt.Println("3 start")
	ctx.Next()

	fmt.Println("3 end")
	ctx.Abort(errors.New("xxx"))
}

type namekey struct{}

func TestCtx(t *testing.T) {

	ctx := chains_engine.NewChainsContext()

	v := ctx.GetStoreWithDef(namekey{}, "").(string)

	bif.Assert(v == "")

	ctx.SetStore(namekey{}, "xxx")

	bif.Assert(ctx.GetStoreWithDef(namekey{}, "").(string) == "xxx")
}

func TestFinal(t *testing.T) {

	engine := chains_engine.NewChainsEngine()

	engine.Register(&chains_engine.ProcessHandler{
		Handler:  ph1,
		StepName: "ph1",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph2,
		StepName: "ph2",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph3,
		StepName: "ph3",
	}).Final(func(ctx chains_engine.ChainsContext) {
		fmt.Println("this is final")
	})

	ctx := chains_engine.NewChainsContext()

	ctx.Start(engine)

	fmt.Println(ctx.Error())
}

func TestError(t *testing.T) {

	engine := chains_engine.NewChainsEngine()

	engine.Register(&chains_engine.ProcessHandler{
		Handler:  ph1,
		StepName: "ph1",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph2,
		StepName: "ph2",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph3,
		StepName: "ph3",
	}).Final(func(ctx chains_engine.ChainsContext) {
		fmt.Println("this is final")
	}).Error(func(err error, ctx chains_engine.ChainsContext) {
		fmt.Println("this is err", err)
	})

	ctx := chains_engine.NewChainsContext()

	ctx.Start(engine)
}

func TestThen(t *testing.T) {

	engine := chains_engine.NewChainsEngine()

	engine.Then("ph1", ph1).Then("ph2", ph2).Then("ph3", ph3)

	ctx := chains_engine.NewChainsContext()

	ctx.Start(engine)

	fmt.Println(ctx.Error())
}

func TestPanic(t *testing.T) {

	engine := chains_engine.NewChainsEngine()

	engine.Register(&chains_engine.ProcessHandler{
		Handler:  ph1,
		StepName: "ph1",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph2,
		StepName: "ph2",
	}).Register(&chains_engine.ProcessHandler{
		Handler: func(ctx chains_engine.ChainsContext) {
			panic("panic x")
		},
		StepName: "ph3",
	}).Final(func(ctx chains_engine.ChainsContext) {
		fmt.Println("this is final")
	}).Error(func(err error, ctx chains_engine.ChainsContext) {
		fmt.Println("this is err", err)
	}).Catch(errors.New("xxx"), func(ctx chains_engine.ChainsContext) {
		fmt.Println("this is catch xxx", ctx.Error())
	}).Catch(errors.New("yyy"), func(ctx chains_engine.ChainsContext) {
		fmt.Println("this is catch yyy", ctx.Error())
	}).Recover(func(ctx chains_engine.ChainsContext, recover any) {
		fmt.Println("this is panic", recover)
	})

	ctx := chains_engine.NewChainsContext()

	ctx.Start(engine)
}

func TestRegister(t *testing.T) {

	engine := chains_engine.NewChainsEngine()

	engine.Register(&chains_engine.ProcessHandler{
		Handler:  ph1,
		StepName: "ph1",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph2,
		StepName: "ph2",
	}).Register(&chains_engine.ProcessHandler{
		Handler:  ph3,
		StepName: "ph3",
	})

	ctx := chains_engine.NewChainsContext()

	ctx.Start(engine)
}
